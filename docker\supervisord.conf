[supervisord]
nodaemon=true

[program:migrate]
command=/opt/bitnami/php/bin/php artisan migrate --seed
autostart=true
autorestart=false
startsecs=0
priority=1
stdout_logfile=/var/log/migrate.log
stderr_logfile=/var/log/migrate.err.log

[program:php-fpm]
command=/opt/bitnami/php/sbin/php-fpm -F
autostart=true
autorestart=true
stdout_logfile=/var/log/php-fpm.log
stderr_logfile=/var/log/php-fpm.err.log
priority=2

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
stdout_logfile=/var/log/nginx.log
stderr_logfile=/var/log/nginx.err.log
priority=3

#[program:laravel-queue]
#command=/opt/bitnami/php/bin/php /app/artisan queue:work --sleep=3 --tries=3
#directory=/var/www
#autostart=true
#autorestart=true
#stdout_logfile=/var/log/laravel-queue.log
#stderr_logfile=/var/log/laravel-queue.err.log

#[program:laravel-scheduler]
#command=/opt/bitnami/php/bin/php /app/artisan schedule:work
#directory=/var/www
#autostart=true
#autorestart=true
#stdout_logfile=/var/log/laravel-scheduler.log
#stderr_logfile=/var/log/laravel-scheduler.err.log
