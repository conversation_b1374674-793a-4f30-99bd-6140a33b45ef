<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/composables/useAuth';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

const { hasHeadquarterAccess, hasCompanyAccess, hasTeamAccess } = useAuth();

interface Role {
    id: number;
    name: string;
    is_headquarter: boolean;
}

interface User {
    id: number;
    uuid: string;
    username: string;
    email: string | null;
    status: string;
    created_at: string;
    updated_at: string;
    roles: Role[];
    headquarter: {
        id: number;
        display_name: string;
        code: string;
    } | null;
    company: {
        id: number;
        display_name: string;
        code: string;
    } | null;
    team: {
        id: number;
        name: string;
    } | null;
}

interface Props {
    user: User;
}

const props = defineProps<Props>();
const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`User: ${props.user.username}`" />
        <div class="px-4 py-3">
            <Heading title="User" pageNumber="P000016" description="View details of the user record" />

            <AppCard title="View User" backRoute="users.index" :form="form" :itemId="props.user.id">
                <dl class="grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Username</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.user.username }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Role</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.user.roles[0].name }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Password</Label>
                        <dd class="col-span-2 text-right text-sm">******</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Confirm Password</Label>
                        <dd class="col-span-2 text-right text-sm">******</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3" v-if="!hasHeadquarterAccess">
                        <Label class="font-medium">Headquarter Name</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.user.headquarter !== null ? props.user.headquarter.display_name : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3" v-if="!hasCompanyAccess && !props.user.roles[0].is_headquarter">
                        <Label class="font-medium">Company Name</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.user.company !== null ? props.user.company.display_name : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3" v-if="!hasTeamAccess">
                        <Label class="font-medium">Team Name</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.user.team !== null ? props.user.team.name : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Email</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.user.email ? props.user.email : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Status</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.user.status === 0 ? 'Active' : 'Inactive' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3"></div>
                </dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
