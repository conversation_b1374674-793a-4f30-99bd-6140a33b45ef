<?php

namespace Database\Seeders\Company;

use App\Models\Headquarter;
use Illuminate\Database\Seeder;

class CompanySeeder extends Seeder
{
    public function run(): void
    {
        $companies = [
            'HEADQUARTER001' => [
                ['name' => 'COMPANY001', 'code' => 'CM001', 'display_name' => 'Company001'],
                ['name' => 'COMPANY002', 'code' => 'CM002', 'display_name' => 'Company002'],
            ],
            'HEADQUARTER002' => [
                ['name' => 'COMPANY003', 'code' => 'CM003', 'display_name' => 'Company003'],
            ],
        ];

        foreach ($companies as $headquarterName => $companyList) {
            $headquarter = Headquarter::firstWhere('name', $headquarterName);

            if (! $headquarter) {
                $this->command->error("Headquarter '{$headquarterName}' not found. Please run HeadquarterSeeder first.");

                continue;
            }

            foreach ($companyList as $companyData) {
                $headquarter->companies()->firstOrCreate(
                    ['name' => $companyData['name']],
                    [
                        'code' => $companyData['code'],
                        'display_name' => $companyData['display_name'],
                    ]
                );
            }
        }

        $this->command->info('Companies created successfully');
    }
}
