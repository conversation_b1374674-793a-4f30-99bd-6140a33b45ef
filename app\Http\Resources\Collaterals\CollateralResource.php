<?php

namespace App\Http\Resources\Collaterals;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CollateralResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $valuer = $this->valuers->firstWhere('is_primary', true);

        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'selection_type_id' => $this->selection_type_id,
            'selection_customer_type_id' => $this->selection_customer_type_id,
            'customer_type_selection' => $this->customerTypeSelection ? $this->customerTypeSelection->value : null,
            'type_selection' => $this->typeSelection ? $this->typeSelection->value : null,
            'name' => $this->name,
            'identity_no' => $this->identity_no,
            'status' => $this->status->value,
            'status_label' => $this->status->label(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
            'property' => $this->property ? [
                'address' => $this->property->address ? [
                    'line_1' => $this->property->address->line_1,
                ] : null,
            ] : null,
            'valuer' => $valuer ? [
                'valuer' => $valuer->valuer,
                'valuation_received_date' => $valuer->valuation_received_date,
                'land_search_received_date' => $valuer->land_search_received_date,
            ] : null,
        ];
    }
}
