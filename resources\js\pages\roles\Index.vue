<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Button } from '@/components/ui/button';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Role {
    id: number;
    name: string;
    level: string;
    permissions: string[];
    created_at: string;
    updated_at: string;
}

interface Props {
    roles: PaginatedData<Role>;
    filters: FilterOptions & {
        name?: string;
    };
}

const props = defineProps<Props>();
const form = useForm({});

const searchValue = ref(props.filters.name || '');
const debouncedSearch = useDebounceFn(() => {
    form.get(
        route('roles.index', {
            name: searchValue.value,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
}, 300);

const handleSearch = () => {
    debouncedSearch();
};

const handleReset = () => {
    searchValue.value = '';
    form.get(route('roles.index'));
};

const handleView = (role: Role) => {
    form.get(route('roles.show', role.id));
};

const handleEdit = (role: Role) => {
    form.get(route('roles.edit', role.id));
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';
    form.get(
        route('roles.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const handleDelete = (role: Role) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Are you sure you want to delete this role? This action cannot be undone.`,
        },
        submitOptions: {
            method: 'delete',
            url: route('roles.destroy', role.id),
            transform: (data) => ({
                ...data,
            }),
            successMessage: `role deleted successfully.`,
            errorMessage: `Failed to delete role. Please try again.`,
            entityName: 'role',
        },
    });
};

const columns = [
    { field: 'name', label: 'Role Name', sortable: true, width: 'w-40' },
    { field: 'level', label: 'Level', sortable: true, width: 'w-20', align: 'text-center' },
    { field: 'permissions', label: 'Permissions', sortable: false },
    {
        field: 'can_see_same_level',
        label: 'Can See Same Level',
        sortable: true,
        width: 'w-40',
        format: (value) => (value === true ? 'Yes' : 'No'),
    },
    { field: 'created_at', label: 'Created At', sortable: true, width: 'w-40' },
    { field: 'updated_at', label: 'Updated At', sortable: true, width: 'w-40' },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];

const groupPermissions = (permissions: string[]): Record<string, string[]> => {
    const fixedOrder = ['read', 'create', 'update', 'delete'];
    const grouped: Record<string, string[]> = {};

    for (const permission of permissions) {
        const [action, resource] = permission.split(' ');
        if (!action || !resource) continue;

        if (!grouped[resource]) {
            grouped[resource] = [];
        }

        grouped[resource].push(action);
    }

    for (const resource in grouped) {
        const actions = grouped[resource];

        const ordered = [
            ...fixedOrder.filter((action) => actions.includes(action)),
            ...actions.filter((action) => !fixedOrder.includes(action)).sort(),
        ];

        grouped[resource] = ordered;
    }

    return Object.keys(grouped)
        .sort()
        .reduce<Record<string, string[]>>((sorted, key) => {
            sorted[key] = grouped[key];
            return sorted;
        }, {});
};

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Roles" />

        <div class="px-4 py-3">
            <Heading title="Roles" pageNumber="P000026" />

            <SearchCard
                v-model:searchValue="searchValue"
                searchLabel="Role Name"
                searchPlaceholder="Role Name"
                @search="handleSearch"
                @reset="handleReset"
            >
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('roles.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Role
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="roles.data"
                        :sort-state="sortState"
                        empty-message="No role found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        @delete="handleDelete"
                        :showDeleteButton="true"
                        :showStatusToggle="false"
                    >
                        <!-- Custom cell renderers -->
                        <template #cell-permissions="{ row }">
                            <div class="space-y-1">
                                <div
                                    v-for="[resource, actions] in Object.entries(groupPermissions(row.permissions))"
                                    :key="resource"
                                    class="flex items-center gap-2"
                                >
                                    <span class="min-w-[140px] text-xs font-medium text-gray-500 capitalize"> {{ resource }}: </span>
                                    <div class="flex flex-wrap gap-2">
                                        <span
                                            v-for="(action, index) in actions"
                                            :key="index"
                                            :class="`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ${
                                                action === 'create'
                                                    ? 'bg-green-100 text-green-800'
                                                    : action === 'read'
                                                      ? 'bg-blue-100 text-blue-800'
                                                      : action === 'update'
                                                        ? 'bg-amber-100 text-amber-800'
                                                        : action === 'delete'
                                                          ? 'bg-red-100 text-red-800'
                                                          : action === 'manage'
                                                            ? 'bg-purple-100 text-purple-800'
                                                            : 'bg-gray-100 text-gray-800'
                                            }`"
                                        >
                                            {{ action }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="roles.from" :to="roles.to" :total="roles.total" entityName="roles" />
                            </div>
                            <Pagination :links="roles.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
