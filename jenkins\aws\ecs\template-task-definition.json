{"containerDefinitions": [{"name": "${CONTAINER_NAME}", "image": "${CONTAINER_IMAGE_REGISTRY_URL}/${IMAGE_NAME}:${VERSION}", "cpu": 0, "portMappings": [{"name": "${CONTAINER_NAME}", "containerPort": 80, "hostPort": 80, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "mountPoints": [], "volumesFrom": [], "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost/status || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 10}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/${AWS_LOG_GROUP_NAME}", "awslogs-region": "${AWS_REGION}", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}}], "family": "${TASK_DEFINITION_FAMILY_NAME}", "executionRoleArn": "arn:aws:iam::${AWS_ACCOUNT_ID}:role/ecsTaskExecutionRole", "networkMode": "bridge", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "cpu": "256", "memory": "256", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}}