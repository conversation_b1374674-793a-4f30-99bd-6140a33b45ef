<?php

namespace App\Models;

use App\Enums\Collateral\CollateralStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * Collateral model for managing collateral information
 */
class Collateral extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'selection_customer_type_id',
        'customer_type',
        'selection_type_id',
        'type',
        'name',
        'identity_no',
        'status',
        'remark',
        'company_name',
        'business_registration_no',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'uuid' => 'string',
        'selection_customer_type_id' => 'integer',
        'selection_type_id' => 'integer',
        'valuation_amount' => 'decimal:2',
        'valuation_received_date' => 'datetime',
        'land_search_received_date' => 'datetime',
        'status' => CollateralStatus::class,
        'deleted_at' => 'datetime',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
    ];

    /**
     * Get the collateral type selection.
     */
    public function customerTypeSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_customer_type_id');
    }

    public function typeSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_type_id');
    }

    /**
     * Get the property details for this collateral.
     */
    public function property(): HasOne
    {
        return $this->hasOne(CollateralProperty::class);
    }

    /**
     * Get the valuers for this collateral.
     */
    public function valuers(): HasMany
    {
        return $this->hasMany(CollateralValuer::class);
    }

    /**
     * Get the address for this collateral.
     */
    public function address(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    /**
     * Get the customer collaterals for this collateral.
     */
    public function customerCollaterals(): HasMany
    {
        return $this->hasMany(CustomerCollateral::class, 'collateral_id');
    }

    /**
     * Get the loan customer collateral for this collateral.
     */
    public function loanCustomerCollateral()
    {
        return $this->belongsTo(LoanCustomerCollateral::class);
    }
}
