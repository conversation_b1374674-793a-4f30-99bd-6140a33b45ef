<?php

namespace App\Http\Resources\Customers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'code' => $this->code,
            'name' => $this->name,
            'identity_no' => $this->identity_no,
            'contact_no' => $this->whenLoaded(
                'customerContacts',
                function () {
                    // Find primary contact across all customer contacts
                    $primaryContact = $this->customerContacts
                        ->flatMap->contacts
                        ->firstWhere('is_primary', false);

                    return $primaryContact ? $primaryContact->contact : null;
                }
            ),
            'nationality' => $this->whenLoaded('selectionNationality', fn () => $this->selectionNationality->value ?? ''),
            'status' => $this->status,
            'headquarter' => $this->whenLoaded('company', function () {
                $headquarter = $this->company?->parent;

                return $headquarter ? [
                    'id' => $headquarter->id,
                    'display_name' => $headquarter->display_name,
                    'code' => $headquarter->code,
                ] : null;
            }),
            'company' => $this->whenLoaded('company', function () {
                return $this->company ? [
                    'id' => $this->company->id,
                    'display_name' => $this->company->display_name,
                    'code' => $this->company->code,
                ] : null;
            }),
            'teams' => $this->whenLoaded(
                'teams',
                fn () => $this->teams->map(fn ($team) => [
                    'id' => $team->id,
                    'name' => $team->name,
                ])
            ),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
        ];
    }
}
