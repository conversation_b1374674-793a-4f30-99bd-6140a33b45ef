<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

interface Props {
    headquarter: {
        id: number;
        code: string;
        display_name: string;
        business_registration_no: string | null;
        old_business_registration_no: string | null;
        status: number;
        remark: string;
        created_at: string;
        updated_at: string;
        created_by: {
            id: number;
            name: string;
        } | null;
        updated_by: {
            id: number;
            name: string;
        } | null;
    };
    logo: any;
}

const props = defineProps<Props>();
const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`Headquarter: ${props.headquarter.display_name}`" />
        <div class="px-4 py-3">
            <Heading title="Headquarter" pageNumber="P000004" description="View details of the headquarter record" />

            <AppCard title="View Headquarter" backRoute="headquarters.index" :form="form" :itemId="props.headquarter.id">
                <dl class="grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Headquarter Code</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.headquarter.code }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Headquarter Name</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.headquarter.display_name }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">New Business Registration No.</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.headquarter.business_registration_no || '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Old Business Registration No.</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.headquarter.old_business_registration_no || '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Status</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.headquarter.status === 0 ? 'Active' : 'Inactive' }}
                        </dd>
                    </div>
                    <div></div>
                    <div class="grid grid-cols-3 items-center py-3">
                        <Label class="font-medium">Logo</Label>
                        <dd class="col-span-2 flex justify-end text-sm">
                            <div v-if="props.logo" class="card rounded-sm border p-1">
                                <img :src="props.logo" alt="Logo preview" class="h-[74px] w-[74px] rounded object-contain" />
                            </div>
                        </dd>
                    </div>
                </dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
