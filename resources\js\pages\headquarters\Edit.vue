<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FileUpload from '@/components/form/file_upload/FileUpload.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

const { submitWithConfirmation } = useFormSubmit();
const { formatEnumOptions } = useFormatOptions();

interface Props {
    headquarter: {
        id: number;
        uuid: string;
        name: string;
        display_name: string;
        business_registration_no: string;
        old_business_registration_no: string;
        status: number;
    };
    logo: any;
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    display_name: props.headquarter.display_name,
    business_registration_no: props.headquarter.headquarter_company.business_registration_no,
    old_business_registration_no: props.headquarter.headquarter_company.old_business_registration_no,
    status: Number(props.headquarter.status),
    logo: props.logo ?? null,
});

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('headquarters.update', props.headquarter.id),
            entityName: 'headquarter',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Edit Headquarter" />
        <div class="px-4 py-3">
            <Heading title="Headquarter" pageNumber="P000003" description="Edit the selected headquarter record" />

            <AppCard title="Edit Headquarter" :form="form" backRoute="headquarters.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Headquarter Name -->
                            <FormField
                                id="display_name"
                                label="Headquarter Name"
                                v-model="form.display_name"
                                type="show"
                                required
                                placeholder="Headquarter Name"
                                :error="form.errors.display_name"
                            />

                            <!-- New Business Registration No -->
                            <FormField
                                id="business_registration_no"
                                label="New Business Registration No."
                                v-model="form.business_registration_no"
                                type="input"
                                placeholder="New Business Registration No."
                                :error="form.errors.business_registration_no"
                            />

                            <!-- Old Business Registration No -->
                            <FormField
                                id="old_business_registration_no"
                                label="Old Business Registration No."
                                v-model="form.old_business_registration_no"
                                type="input"
                                placeholder="Old Business Registration No."
                                :error="form.errors.old_business_registration_no"
                            />

                            <!-- Status -->
                            <FormField
                                id="status"
                                label="Status"
                                v-model="form.status"
                                type="select"
                                :options="formatEnumOptions(props.statuses)"
                                required
                                placeholder="Status"
                                :error="form.errors.status"
                            />

                            <FileUpload id="logo" label="Logo" v-model="form.logo" isEdit />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
