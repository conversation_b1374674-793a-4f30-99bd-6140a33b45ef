<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';

interface Role {
    id: number;
    name: string;
    level: number;
    permissions: string[];
    created_at: string;
    updated_at: string;
}

interface Props {
    role: Role;
}

const props = defineProps<Props>();
const form = useForm({});

const groupPermissions = (permissions: string[]): Record<string, string[]> => {
    const fixedOrder = ['read', 'create', 'update', 'delete'];
    const grouped: Record<string, string[]> = {};

    for (const permission of permissions) {
        const [action, resource] = permission.split(' ');
        if (!action || !resource) continue;

        if (!grouped[resource]) {
            grouped[resource] = [];
        }

        grouped[resource].push(action);
    }

    for (const resource in grouped) {
        const actions = grouped[resource];

        const ordered = [
            ...fixedOrder.filter((action) => actions.includes(action)),
            ...actions.filter((action) => !fixedOrder.includes(action)).sort(),
        ];

        grouped[resource] = ordered;
    }

    return Object.keys(grouped)
        .sort()
        .reduce<Record<string, string[]>>((sorted, key) => {
            sorted[key] = grouped[key];
            return sorted;
        }, {});
};
</script>

<template>
    <AppLayout>
        <Head :title="`Role: ${role.name}`" />
        <div class="px-4 py-3">
            <Heading title="Role" pageNumber="P000035" description="View details of the role record" />

            <AppCard title="View Role" backRoute="roles.index" :form="form" :itemId="props.role.id">
                <dl class="mb-4 grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Role Name</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.role.name }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Role Level</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.role.level }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Can See Same Level</Label>
                        <dd class="col-span-2 text-right text-sm">{{ role.can_see_same_level ? 'Yes' : 'No' }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Created At</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ formatDateTime(props.role.created_at) }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Updated At</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ formatDateTime(props.role.updated_at) }}
                        </dd>
                    </div>
                </dl>
                <div class="grid gap-2">
                    <Label class="mb-4 text-base font-medium">Permissions</Label>
                    <div v-if="role.permissions.length" class="space-y-4">
                        <div
                            v-for="[resource, actions] in Object.entries(groupPermissions(role.permissions))"
                            :key="resource"
                            class="border-b pb-3 last:border-b-0 last:pb-0"
                        >
                            <h4 class="mb-2 text-sm font-medium text-gray-700 capitalize">{{ resource }}</h4>
                            <div class="flex flex-wrap gap-2">
                                <Badge
                                    v-for="(action, index) in actions"
                                    :key="index"
                                    variant="secondary"
                                    :class="`capitalize ${
                                        action === 'create'
                                            ? 'bg-green-100 text-green-800'
                                            : action === 'read'
                                              ? 'bg-blue-100 text-blue-800'
                                              : action === 'update'
                                                ? 'bg-amber-100 text-amber-800'
                                                : action === 'delete'
                                                  ? 'bg-red-100 text-red-800'
                                                  : action === 'manage'
                                                    ? 'bg-purple-100 text-purple-800'
                                                    : 'bg-gray-100 text-gray-800'
                                    }`"
                                >
                                    {{ action }}
                                </Badge>
                            </div>
                        </div>
                    </div>

                    <div v-else class="text-gray-500 italic">No permissions assigned to this role.</div>
                </div>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
