<?php

namespace Database\Seeders\Customer;

use App\Models\Contact;
use App\Models\CustomerProfile;
use App\Models\Team;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    public function run(): void
    {
        $team = Team::where('name', 'Company Team 2')->first();

        if (! $team) {
            $this->command->error('Team not found. Please run TeamSeeder first.');

            return;
        }

        $customersData = [
            [
                'code' => 'CUSTOMER001',
                'name' => 'Customer 001',
                'email' => '<EMAIL>',
                'identity_no_prefix' => 'ID',
                'age_range' => [20, 60],
                'birth_year_offset_range' => [20, 60],
                'selection_type_id' => 28,
                'selection_gender_id' => 47,
                'selection_race_id' => 43,
                'selection_nationality_id' => 49,
                'selection_education_level_id' => 54,
                'selection_marriage_status_id' => 319,
                'contact_number' => '1234567890',
                'address_country_id' => 25,
                'address_state_id' => 1,
                'employment' => [
                    'employer_name' => 'Employer 001',
                    'job_position' => 'Job Position 001',
                    'terms_of_employment_id' => 60,
                    'occupation_id' => 68,
                    'income_range' => [1000, 10000],
                    'service_year_range' => [1, 10],
                    'service_month_range' => [1, 12],
                    'employment_address_country_id' => 26,
                    'employment_address_state_id' => 1,
                ],
            ],
            [
                'code' => 'CUSTOMER002',
                'name' => 'Customer 002',
                'email' => '<EMAIL>',
                'identity_no_prefix' => 'ID',
                'age_range' => [25, 55],
                'birth_year_offset_range' => [25, 55],
                'selection_type_id' => 28,
                'selection_gender_id' => 47,
                'selection_race_id' => 43,
                'selection_nationality_id' => 49,
                'selection_education_level_id' => 54,
                'selection_marriage_status_id' => 319,
                'contact_number' => '0987654321',
                'address_country_id' => 25,
                'address_state_id' => 1,
                'employment' => [
                    'employer_name' => 'Employer 002',
                    'job_position' => 'Job Position 002',
                    'terms_of_employment_id' => 60,
                    'occupation_id' => 68,
                    'income_range' => [2000, 12000],
                    'service_year_range' => [2, 15],
                    'service_month_range' => [1, 12],
                    'employment_address_country_id' => 26,
                    'employment_address_state_id' => 2,
                ],
            ],
        ];

        foreach ($customersData as $data) {
            if (CustomerProfile::where('code', $data['code'])->exists()) {
                continue;
            }

            $age = rand(...$data['age_range']);
            $birthDate = now()->subYears(rand(...$data['birth_year_offset_range']))->format('Y-m-d');
            $identityNo = $data['identity_no_prefix'].rand(100000, 999999);

            $customer = CustomerProfile::create([
                'code' => $data['code'],
                'name' => $data['name'],
                'email' => $data['email'],
                'identity_no' => $identityNo,
                'age' => $age,
                'birth_date' => $birthDate,
                'selection_type_id' => $data['selection_type_id'],
                'selection_gender_id' => $data['selection_gender_id'],
                'selection_race_id' => $data['selection_race_id'],
                'selection_nationality_id' => $data['selection_nationality_id'],
                'selection_education_level_id' => $data['selection_education_level_id'],
                'selection_marriage_status_id' => $data['selection_marriage_status_id'],
            ]);

            $customer->teams()->attach($team->id);

            $customerContact = $customer->customerContacts()->create();
            $customerContact->contacts()->create([
                'selection_type_id' => 32,
                'category' => Contact::CATEGORY_MOBILE,
                'selection_country_id' => 36,
                'contact' => $data['contact_number'],
                'can_receive_sms' => true,
                'is_primary' => true,
            ]);

            $customerAddress = $customer->customerAddresses()->create();
            $customerAddress->addresses()->create([
                'selection_state_id' => $data['address_state_id'],
                'selection_type_id' => 40,
                'selection_country_id' => $data['address_country_id'],
                'line_1' => '123 Main Street',
                'line_2' => 'Suite 456',
                'postcode' => '12345',
                'city' => 'Metropolis',
                'is_primary' => true,
            ]);

            $emp = $data['employment'];

            $customerEmployment = $customer->employment()->create([
                'employer_name' => $emp['employer_name'],
                'length_service_year' => rand(...$emp['service_year_range']),
                'length_service_month' => rand(...$emp['service_month_range']),
                'job_position' => $emp['job_position'],
                'selection_terms_of_employment_id' => $emp['terms_of_employment_id'],
                'selection_occupation_id' => $emp['occupation_id'],
                'gross_income' => rand(...$emp['income_range']),
                'net_income' => rand(...$emp['income_range']),
            ]);

            $customerEmployment->address()->create([
                'selection_state_id' => $emp['employment_address_state_id'],
                'selection_country_id' => $emp['employment_address_country_id'],
                'line_1' => '123 Main Street',
                'line_2' => 'Suite 456',
                'postcode' => '12345',
                'city' => 'Metropolis',
                'is_primary' => true,
            ]);

            $customerEmployment->contacts()->create([
                'category' => Contact::CATEGORY_MOBILE,
                'selection_country_id' => 36,
                'contact' => $data['contact_number'],
                'can_receive_sms' => true,
            ]);

            $this->command->info("Customer {$data['code']} created successfully");
        }
    }
}
