<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

const { formatEnumOptions, formatSelectionOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();

interface Props {
    categories: Array<{
        id: number;
        value: string;
    }>;
    defaultStatus: number;
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    category: '',
    value: '',
    description: '',
    sort_order: 100,
    status: props.defaultStatus,
});

const newCategory = ref('');
const showNewCategoryInput = ref(false);

const toggleNewCategoryInput = () => {
    form.category = '';
    showNewCategoryInput.value = !showNewCategoryInput.value;
    if (!showNewCategoryInput.value) {
        newCategory.value = '';
    }
};

const formFields = computed(() => [
    {
        id: 'value',
        label: 'Value',
        type: 'input',
        required: true,
        placeholder: 'Value',
        error: form.errors.value,
        modelValue: form.value,
        updateValue: (value: string) => (form.value = value),
    },
    {
        id: 'description',
        label: 'Description',
        type: 'input',
        required: false,
        placeholder: 'Description',
        error: form.errors.description,
        modelValue: form.description,
        updateValue: (value: string) => (form.description = value),
    },
    {
        id: 'sort_order',
        label: 'Sort Order',
        type: 'input',
        required: false,
        placeholder: 'Sort Order',
        error: form.errors.sort_order,
        modelValue: form.sort_order,
        updateValue: (value: number) => (form.sort_order = value),
    },
    {
        id: 'status',
        label: 'Status',
        type: 'select',
        required: true,
        placeholder: 'Status',
        error: form.errors.status,
        options: formatEnumOptions(props.statuses),
        modelValue: form.status,
        updateValue: (value: number) => (form.status = value),
    },
]);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('selections.store'),
            entityName: 'selection',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Create Selection" />
        <div class="px-4 py-3">
            <Heading title="Selection" pageNumber="P000050" description="Create a new selection record" />

            <AppCard title="Add New Selection" :form="form" backRoute="selections.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="flex justify-end">
                            <Button type="button" variant="ghost" size="sm" @click="toggleNewCategoryInput" class="text-xs">
                                {{ showNewCategoryInput ? 'Select Existing' : 'Add New Category' }}
                            </Button>
                        </div>
                        <div class="full-width">
                            <Label class="text-lg">{{ showNewCategoryInput ? 'Add New Category' : 'Select Existing' }}</Label>
                        </div>
                        <div class="grid gap-4 lg:grid-cols-2">
                            <FormField
                                v-if="showNewCategoryInput"
                                key="new-category"
                                id="category"
                                label="New Category"
                                v-model="form.category"
                                type="input"
                                :required="true"
                                placeholder="Enter new category name"
                                :error="form.errors.category"
                            />

                            <!-- else show a dropdown for existing categories for example -->
                            <FormField
                                v-else
                                id="category"
                                label="Category"
                                v-model="form.category"
                                type="select"
                                required
                                placeholder="Select a category"
                                :error="form.errors.category"
                                :options="formatSelectionOptions(props.categories)"
                            />

                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
