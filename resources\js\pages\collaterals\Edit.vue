<script setup lang="ts">
import Heading from '@/components/Heading.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import AdditionalPropertyOwner from '@/components/collateral/AdditionalPropertyOwner.vue';
import CollateralInfo from '@/components/collateral/CollateralInfo.vue';
import Valuation from '@/components/collateral/Valuation.vue';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { useFormSubmit } from '@/composables/useFormSubmit';
import { useLeaveGuard } from '@/composables/useLeaveGuard';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref, watch } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Address {
    id?: number;
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    state: string | null;
    selection_state_id: number | null;
    country: string | null;
    selection_country_id: number | null;
}

interface PropertyOwner {
    id?: number;
    name: string;
    identity_no: string;
    selection_telephone_country_id: number | null;
    selection_mobile_country_id: number | null;
    telephone: string | null;
    mobile_phone: string | null;
    remark: string | null;
    address: Address | null;
    _delete?: boolean;
}

interface Valuer {
    id?: number;
    valuation_amount: number;
    valuer: string;
    valuation_received_date: string | null;
    land_search_received_date: string | null;
    is_primary: boolean | null;
    _delete?: boolean;
}

interface Property {
    id?: number;
    ownership_no: string | null;
    lot_number: string | null;
    selection_land_category_id: number | null;
    land_category: string | null;
    land_category_selection: string | null;
    land_category_other: string | null;
    selection_type_of_property_id: number | null;
    type_of_property: string | null;
    type_of_property_selection: string | null;
    selection_land_size_unit: number | null;
    land_size: string | null;
    selection_land_status_id: number | null;
    land_status: string | null;
    land_status_selection: string | null;
    no_syit_piawai: string | null;
    certified_plan_no: string | null;
    selection_built_up_area_unit: number | null;
    built_up_area_of_property: string | null;
    city: string | null;
    location: string | null;
    district: string | null;
    address: Address | null;
}

interface Collateral {
    id: number;
    uuid: string;
    selection_customer_type_id: number | null;
    selection_type_id: number | null;
    type: string;
    type_selection: string | null;
    name: string;
    identity_no: string;
    company_name: string | null;
    business_registration_no: string | null;
    status: number;
    status_label: string;
    remark: string | null;
    property: Property | null;
    property_owners: PropertyOwner[];
    valuers: Valuer[];
}

interface Selection {
    id: number;
    value: string;
}

interface Props {
    collateral: Collateral;
    statuses: Record<string, string>;
    customerTypes: Selection[];
    collateralTypes: Selection[];
    propertyTypes: Selection[];
    landCategories: Selection[];
    squareTypes: Selection[];
    landStatuses: Selection[];
    states: Selection[];
    countries: Selection[];
    mobileCountries: Selection[];
    telephoneCountries: Selection[];
}

const props = defineProps<Props>();
const activeTab = ref('info');
const ordinal = (n) => {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
};
const hasUnsavedChanges = ref(true); // set to true when the form is dirty
useLeaveGuard(() => hasUnsavedChanges.value);

// Initialize form with collateral data
const form = useForm({
    selection_customer_type_id: props.collateral.selection_customer_type_id,
    selection_type_id: props.collateral.selection_type_id,
    type: props.collateral.type,
    name: props.collateral.name,
    identity_no: props.collateral.identity_no,
    company_name: props.collateral.company_name,
    business_registration_no: props.collateral.business_registration_no,
    status: props.collateral.status,
    remark: props.collateral.remark,

    // Property data
    property: props.collateral.property
        ? {
              id: props.collateral.property.id,
              ownership_no: props.collateral.property.ownership_no,
              lot_number: props.collateral.property.lot_number,
              selection_land_category_id: props.collateral.property.selection_land_category_id,
              land_category: props.collateral.property.land_category,
              land_category_other: props.collateral.property.land_category_other,
              selection_type_of_property_id: props.collateral.property.selection_type_of_property_id,
              type_of_property: props.collateral.property.type_of_property,
              selection_land_size_unit: props.collateral.property.selection_land_size_unit,
              land_size: props.collateral.property.land_size,
              selection_land_status_id: props.collateral.property.selection_land_status_id,
              land_status: props.collateral.property.land_status,
              no_syit_piawai: props.collateral.property.no_syit_piawai,
              certified_plan_no: props.collateral.property.certified_plan_no,
              selection_built_up_area_unit: props.collateral.property.selection_built_up_area_unit,
              built_up_area_of_property: props.collateral.property.built_up_area_of_property,
              city: props.collateral.property.city,
              location: props.collateral.property.location,
              district: props.collateral.property.district,
              address: props.collateral.property.address
                  ? {
                        id: props.collateral.property.address.id,
                        line_1: props.collateral.property.address.line_1,
                        line_2: props.collateral.property.address.line_2,
                        postcode: props.collateral.property.address.postcode,
                        city: props.collateral.property.address.city,
                        selection_state_id: props.collateral.property.address.selection_state_id,
                        state: props.collateral.property.address.state,
                        selection_country_id: props.collateral.property.address.selection_country_id,
                        country: props.collateral.property.address.country,
                    }
                  : null,
          }
        : null,

    // Property owners
    property_owners: props.collateral.property_owners.map((owner) => ({
        id: owner.id,
        name: owner.name,
        identity_no: owner.identity_no,
        selection_telephone_country_id: owner.selection_telephone_country_id ?? props.telephoneCountries[0]?.id ?? null,
        selection_mobile_country_id: owner.selection_mobile_country_id ?? props.mobileCountries[0]?.id ?? null,
        telephone: owner.telephone,
        mobile_phone: owner.mobile_phone,
        remark: owner.remark,
        address: owner.address
            ? {
                  id: owner.address.id,
                  line_1: owner.address.line_1,
                  line_2: owner.address.line_2,
                  postcode: owner.address.postcode,
                  city: owner.address.city,
                  selection_state_id: owner.address.selection_state_id,
                  state: owner.address.state,
                  selection_country_id: owner.address.selection_country_id,
                  country: owner.address.country,
              }
            : null,
        _delete: false,
    })),

    // Valuers
    valuers: props.collateral.valuers.map((valuer) => ({
        id: valuer.id,
        valuation_amount: valuer.valuation_amount,
        valuer: valuer.valuer,
        valuation_received_date: valuer.valuation_received_date,
        land_search_received_date: valuer.land_search_received_date,
        is_primary: valuer.is_primary,
        _delete: false,
    })),
});

// Submit form
const submit = () => {
    form.valuers = form.valuers.map((valuer, index) => ({
        ...valuer,
        is_primary: index === 0,
    }));

    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('collaterals.update', props.collateral.id),
            entityName: 'collateral',
        },
    });
};

const goBack = () => {
    if (!hasUnsavedChanges.value) {
        $inertia.visit(route('collaterals.index'));
        return;
    }

    const fakeLink = document.createElement('a');
    fakeLink.href = route('collaterals.index');
    document.body.appendChild(fakeLink);
    fakeLink.click();
    document.body.removeChild(fakeLink);
};

// Watch for collateral type selection changes
watch(
    () => form.selection_type_id,
    (newValue) => {
        if (newValue) {
            const selectedType = props.collateralTypes.find((type) => type.id === newValue);
            if (selectedType) {
                form.type = selectedType.value;
            }
        }
    },
);

const tabItems = [
    { label: 'Collateral Info', value: 'info' },
    { label: 'Valuation', value: 'valuation' },
    { label: 'Additional Owner', value: 'owner' },
];
</script>

<template>
    <AppLayout>
        <Head title="Edit Collateral" />

        <div class="px-4 py-6">
            <div class="mb-6 flex items-center justify-between">
                <Heading title="Edit Collateral" pageNumber="P000066" description="Edit Collateral Details" />
            </div>

            <form @submit.prevent="submit">
                <!-- Basic Information -->
                <Card class="gap-0 py-0">
                    <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                        <CardTitle>Edit Collateral</CardTitle>
                    </CardHeader>
                    <TabsWrapper v-model="activeTab" :tabs="tabItems">
                        <template #info>
                            <!-- Your info content -->
                            <CollateralInfo
                                :form="form"
                                :errors="form.errors"
                                :customerTypes="customerTypes"
                                :collateralTypes="collateralTypes"
                                :squareTypes="squareTypes"
                                :propertyTypes="propertyTypes"
                                :landCategories="landCategories"
                                :landStatuses="landStatuses"
                                :states="states"
                                :countries="countries"
                                isEdit
                                :ordinal="ordinal"
                                :goBack="goBack"
                                :goNext="() => (activeTab = 'valuation')"
                            />
                        </template>

                        <template #valuation>
                            <!-- Your valuation content -->
                            <Valuation
                                :valuers="form.valuers"
                                :errors="form.errors"
                                isEdit
                                collateralSide
                                :ordinal="ordinal"
                                :goBack="() => (activeTab = 'info')"
                                :goNext="() => (activeTab = 'owner')"
                                :nextLabel="'Next'"
                            />
                        </template>

                        <template #owner>
                            <!-- Your owner content -->
                            <AdditionalPropertyOwner
                                :property_owners="form.property_owners"
                                :errors="form.errors"
                                :states="states"
                                :countries="countries"
                                :mobileCountries="mobileCountries"
                                :telephoneCountries="telephoneCountries"
                                isEdit
                                collateralSide
                                :ordinal="ordinal"
                                :goBack="() => (activeTab = 'valuation')"
                                :submit="submit"
                            />
                        </template>
                    </TabsWrapper>
                </Card>
            </form>
        </div>
    </AppLayout>
</template>
