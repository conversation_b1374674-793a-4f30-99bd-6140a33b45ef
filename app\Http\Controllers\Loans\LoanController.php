<?php

namespace App\Http\Controllers\Loans;

use App\Enums\Loan\LoanStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Loans\StoreLoanRequest;
use App\Http\Requests\Loans\UpdateLoanRequest;
use App\Http\Resources\Loans\LoanResource;
use App\Models\AgentProfile;
use App\Models\CollateralProperty;
use App\Models\CollateralPropertyOwner;
use App\Models\Company;
use App\Models\CustomerCollateral;
use App\Models\CustomerDocument;
use App\Models\CustomerProfile;
use App\Models\Headquarter;
use App\Models\Loan;
use App\Models\Selection;
use App\Models\Team;
use App\Traits\QueryFilterableTrait;
use App\Traits\SelectionTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class LoanController extends Controller
{
    use QueryFilterableTrait, SelectionTrait;

    /**
     * Display a listing of the loans.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', Loan::class);

        $query = Loan::query()
            ->withAuditUsers()
            ->with([
                'client:id,code,name',
                'company:id,code,display_name',
                'team:id,name',
                'agent:id,username',
                'selectionType:id,value',
            ]);

        $this->applySearchFilter($query, $request, 'code');
        $this->applySearchFilter($query, $request, 'client_name', 'client');
        $this->applyRelationFilter($query, $request, 'company_name', 'company', 'display_name');
        $this->applyRelationFilter($query, $request, 'team_name', 'team', 'name');
        $this->applyRelationFilter($query, $request, 'agent_name', 'agent', 'username');
        $this->applyStatusFilter($query, $request);
        $this->applySorting($query, $request);

        $loans = $this->applyPagination($query, $request, 10,
            fn ($loan) => (new LoanResource($loan))->toArray($request));

        return Inertia::render('loans/Index', [
            'loans' => $loans,
            'filters' => $request->only(['code', 'client_name', 'company_name', 'team_name', 'agent_name', 'status', 'per_page', 'sort_field', 'sort_direction']),
            'statuses' => LoanStatus::options(),
        ]);
    }

    /**
     * Show the form for creating a new loan.
     */
    public function create(): Response
    {
        $this->authorize('create', Loan::class);

        return Inertia::render('loans/Create', [
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
            'teams' => Team::getForDropdown(),
            'agents' => AgentProfile::getForDropdown(),
            'loanTypes' => $this->getSelectionsOptionsForCategory('loan_type'),
            'loanModes' => $this->getSelectionsOptionsForCategory('loan_mode'),
            'repaymentMethods' => $this->getSelectionsOptionsForCategory('repayment_method'),
            'relationships' => $this->getSelectionsOptionsForCategory('relationship'),
            'telephoneCountries' => $this->getSelectionsOptionsForCategory('telephone_country'),
            'mobileCountries' => $this->getSelectionsOptionsForCategory('mobile_country'),
            'contactTypes' => $this->getSelectionsOptionsForCategory('contact_type'),
            'states' => $this->getSelectionsOptionsForCategory('state'),
            'countries' => $this->getSelectionsOptionsForCategory('country'),
            'nationalities' => $this->getSelectionsOptionsForCategory('nationality'),
            'employmentTerms' => $this->getSelectionsOptionsForCategory('terms_of_employment'),
            'businessClassifications' => $this->getSelectionsOptionsForCategory('business_classification'),
            'documentTypes' => $this->getSelectionsOptionsForCategory('document_type'),
        ]);
    }

    /**
     * Store a newly created loan in storage.
     */
    public function store(StoreLoanRequest $request): RedirectResponse
    {
        $this->authorize('create', Loan::class);

        try {
            DB::beginTransaction();

            $company = Company::find($request->company_id);
            $team = Team::find($request->team_id);
            $agent = AgentProfile::find($request->agent_id);
            $selectionLoanType = Selection::find($request->selection_type_id);

            // Create loan
            $loan = Loan::create([
                'company_id' => $company?->id,
                'company' => $company?->display_name,
                'team_id' => $team?->id,
                'team' => $team?->name,
                'agent_id' => $agent?->id,
                'agent' => $agent?->username,
                'selection_type_id' => $selectionLoanType?->id,
                'type' => $selectionLoanType?->value,
                'status' => LoanStatus::ACTIVE,
            ]);

            $selectionModeType = Selection::find($request->input('loan.selection_mode_type_id'));
            $selectionRepaymentMethod = Selection::find($request->input('loan.selection_repayment_method_id'));

            // Create loan detail
            $loan->loanDetail()->create([
                'selection_mode_type_id' => $selectionModeType?->id,
                'mode_type' => $selectionModeType?->value,
                'loan_principle_amount' => $request->input('loan.loan_principle_amount'),
                'no_of_instalment' => $request->input('loan.no_of_instalment'),
                'last_payment' => $request->input('loan.last_payment'),
                'selection_repayment_method_id' => $selectionRepaymentMethod?->id,
                'repayment_method' => $selectionRepaymentMethod?->value,
                'instalment_amount' => $request->input('loan.instalment_amount'),
                'interest' => $request->input('loan.interest'),
                'late_payment_charges' => $request->input('loan.late_payment_charges'),
            ]);

            $borrower = CustomerProfile::with([
                'employment.address', 'employment.contacts',
                'company.owners',
                'customerAddresses.addresses', 'customerContacts.contacts',
            ])->find($request->borrower_id);

            $selectionBorrowerType = Selection::find($borrower->selection_type_id);
            $selectionBorrowerGender = Selection::find($borrower->selection_gender_id);
            $selectionBorrowerRace = Selection::find($borrower->selection_race_id);
            $selectionBorrowerNationality = Selection::find($borrower->selection_nationality_id);
            $selectionBorrowerEducationLevel = Selection::find($borrower->selection_education_level_id);
            $selectionBorrowerMarriageStatus = Selection::find($borrower->selection_marriage_status_id);

            // Create main borrower profile
            $loanCustomerProfile = $loan->loanCustomerProfiles()->create([
                'customer_id' => $borrower->id,
                'code' => $borrower->code,
                'name' => $borrower->name,
                'email' => $borrower->email,
                'identity_no' => $borrower->identity_no,
                'old_identity_no' => $borrower->old_identity_no,
                'registration_date' => $borrower->registration_date,
                'years_of_incorporation' => $borrower->years_of_incorporation,
                'age' => $borrower->age,
                'birth_date' => $borrower->birth_date,
                'is_primary' => true,
                'selection_type_id' => $selectionBorrowerType?->id,
                'type' => $selectionBorrowerType?->value,
                'selection_gender_id' => $selectionBorrowerGender?->id,
                'gender' => $selectionBorrowerGender?->value,
                'selection_race_id' => $selectionBorrowerRace?->id,
                'race' => $selectionBorrowerRace?->value,
                'selection_nationality_id' => $selectionBorrowerNationality?->id,
                'nationality' => $selectionBorrowerNationality?->value,
                'selection_education_level_id' => $selectionBorrowerEducationLevel?->id,
                'education_level' => $selectionBorrowerEducationLevel?->value,
                'selection_marriage_status_id' => $selectionBorrowerMarriageStatus?->id,
                'marriage_status' => $selectionBorrowerMarriageStatus?->value,
                'remark' => $borrower->remark,
            ]);

            $borrowerEmployment = $borrower->employment;
            $borrowerCompany = $borrower->company;

            if ($borrower->selection_type_id == 28) {
                $selectionTermsOfEmployment = Selection::find($borrowerEmployment->selection_terms_of_employment_id);
                $selectionOccupation = Selection::find($borrowerEmployment->selection_occupation_id);
                $selectionBusinessCategory = Selection::find($borrowerEmployment->selection_business_category_id);

                $loanCustomerEmployment = $loanCustomerProfile->loanCustomerEmployment()->create([
                    'employer_name' => $borrowerEmployment->employer_name,
                    'length_service_year' => $borrowerEmployment->length_service_year,
                    'length_service_month' => $borrowerEmployment->length_service_month,
                    'job_position' => $borrowerEmployment->job_position,
                    'selection_terms_id' => $selectionTermsOfEmployment?->id,
                    'terms' => $selectionTermsOfEmployment?->value,
                    'selection_occupation_id' => $selectionOccupation?->id,
                    'occupation' => $selectionOccupation?->value,
                    'selection_business_category_id' => $selectionBusinessCategory?->id,
                    'business_category' => $selectionBusinessCategory?->value,
                    'gross_income' => $borrowerEmployment->gross_income,
                    'net_income' => $borrowerEmployment->net_income,
                ]);

                // Create loan customer employment address
                $borrowerEmploymentAddress = $borrowerEmployment->address;

                $selectionEmploymentAddressType = Selection::find($borrowerEmploymentAddress->selection_type_id);
                $selectionEmploymentAddressState = Selection::find($borrowerEmploymentAddress->selection_state_id);
                $selectionEmploymentAddressCountry = Selection::find($borrowerEmploymentAddress->selection_country_id);

                $loanCustomerEmployment->address()->create([
                    'category' => $borrowerEmploymentAddress->category,
                    'selection_type_id' => $selectionEmploymentAddressType?->id,
                    'type' => $selectionEmploymentAddressType?->value,
                    'line_1' => $borrowerEmploymentAddress->line_1,
                    'line_2' => $borrowerEmploymentAddress->line_2,
                    'postcode' => $borrowerEmploymentAddress->postcode,
                    'city' => $borrowerEmploymentAddress->city,
                    'selection_state_id' => $selectionEmploymentAddressState?->id,
                    'state' => $selectionEmploymentAddressState?->value,
                    'selection_country_id' => $selectionEmploymentAddressCountry?->id,
                    'country' => $selectionEmploymentAddressCountry?->value,
                    'is_primary' => $borrowerEmploymentAddress->is_primary,
                ]);

                // Create loan customer employment contacts
                $borrowerEmploymentContacts = $borrowerEmployment->contacts;
                foreach ($borrowerEmploymentContacts as $borrowerEmploymentContact) {
                    $selectionEmploymentContactType = Selection::find($borrowerEmploymentContact->selection_type_id);
                    $selectionEmploymentContactCountry = Selection::find($borrowerEmploymentContact->selection_country_id);

                    $loanCustomerEmployment->contacts()->create([
                        'category' => $borrowerEmploymentContact->category,
                        'selection_type_id' => $selectionEmploymentContactType?->id,
                        'type' => $selectionEmploymentContactType?->value,
                        'selection_country_id' => $selectionEmploymentContactCountry?->id,
                        'country' => $selectionEmploymentContactCountry?->value,
                        'contact' => $borrowerEmploymentContact->contact,
                        'is_primary' => $borrowerEmploymentContact->is_primary,
                        'can_receive_sms' => $borrowerEmploymentContact->can_receive_sms,
                    ]);
                }
            } elseif ($borrower->selection_type_id == 29) {
                $selectionNatureOfBusiness = Selection::find($borrowerCompany->selection_nature_of_business_id);
                $selectionCountryOfBusiness = Selection::find($borrowerCompany->selection_country_of_business_id);

                $loanCustomerCompany = $loanCustomerProfile->loanCustomerCompany()->create([
                    'current_paid_up_capital' => $borrowerCompany->current_paid_up_capital,
                    'business_turnover' => $borrowerCompany->business_turnover,
                    'business_turnover_date' => $borrowerCompany->business_turnover_date,
                    'business_net_income' => $borrowerCompany->business_net_income,
                    'business_net_income_date' => $borrowerCompany->business_net_income_date,
                    'selection_nature_of_business_id' => $selectionNatureOfBusiness?->id,
                    'nature_of_business' => $selectionNatureOfBusiness?->value,
                    'selection_country_of_business_id' => $selectionCountryOfBusiness?->id,
                    'country_of_business' => $selectionCountryOfBusiness?->value,
                ]);

                // Create loan customer company owners
                $borrowerCompanyOwners = $borrowerCompany->owners;
                foreach ($borrowerCompanyOwners as $borrowerCompanyOwner) {
                    $selectionOwnerType = Selection::find($borrowerCompanyOwner->selection_type_id);
                    $selectionOwnerNationality = Selection::find($borrowerCompanyOwner->selection_nationality_id);

                    $loanCustomerCompany->owners()->create([
                        'name' => $borrowerCompanyOwner->name,
                        'identity_no' => $borrowerCompanyOwner->identity_no,
                        'selection_type_id' => $selectionOwnerType?->id,
                        'type' => $selectionOwnerType?->value,
                        'selection_nationality_id' => $selectionOwnerNationality?->id,
                        'nationality' => $selectionOwnerNationality?->value,
                        'share_unit' => $borrowerCompanyOwner->share_unit,
                    ]);
                }
            }

            // Create loan customer addresses
            $loanCustomerProfileAddresses = $loanCustomerProfile->loanCustomerAddresses()->create();

            $borrowerCustomerAddresses = $borrower->customerAddresses;
            foreach ($borrowerCustomerAddresses as $borrowerCustomerAddress) {
                $borrowerAddresses = $borrowerCustomerAddress->addresses;

                foreach ($borrowerAddresses as $borrowerAddress) {
                    $selectionAddressType = Selection::find($borrowerAddress->selection_type_id);
                    $selectionAddressState = Selection::find($borrowerAddress->selection_state_id);
                    $selectionAddressCountry = Selection::find($borrowerAddress->selection_country_id);

                    $loanCustomerProfileAddresses->addresses()->create([
                        'category' => $borrowerAddress->category,
                        'selection_type_id' => $selectionAddressType?->id,
                        'type' => $selectionAddressType?->value,
                        'line_1' => $borrowerAddress->line_1,
                        'line_2' => $borrowerAddress->line_2,
                        'postcode' => $borrowerAddress->postcode,
                        'city' => $borrowerAddress->city,
                        'selection_state_id' => $selectionAddressState?->id,
                        'state' => $selectionAddressState?->value,
                        'selection_country_id' => $selectionAddressCountry?->id,
                        'country' => $selectionAddressCountry?->value,
                        'is_primary' => true,
                    ]);
                }
            }

            // Create loan customer contacts
            $loanCustomerProfileContacts = $loanCustomerProfile->loanCustomerContacts()->create();

            $borrowerCustomerContacts = $borrower->customerContacts;

            foreach ($borrowerCustomerContacts as $borrowerCustomerContact) {
                $borrowerContacts = $borrowerCustomerContact->contacts;
                foreach ($borrowerContacts as $borrowerContact) {
                    $selectionContactType = Selection::find($borrowerContact->selection_type_id);
                    $selectionContactCountry = Selection::find($borrowerContact->selection_country_id);

                    $loanCustomerProfileContacts->contacts()->create([
                        'category' => $borrowerContact->category,
                        'selection_type_id' => $selectionContactType?->id,
                        'type' => $selectionContactType?->value,
                        'selection_country_id' => $selectionContactCountry?->id,
                        'country' => $selectionContactCountry?->value,
                        'contact' => $borrowerContact->contact,
                        'is_primary' => $borrowerContact->is_primary,
                        'can_receive_sms' => $borrowerContact->can_receive_sms,
                    ]);
                }
            }

            // Create co-borrowers
            $coBorrowerIds = $request->input('co_borrower.ids', []);
            foreach ($coBorrowerIds as $coBorrowerId) {
                $coBorrower = CustomerProfile::with([
                    'employment.address', 'employment.contacts',
                    'company.owners',
                    'customerAddresses.addresses', 'customerContacts.contacts',
                ])->find($coBorrowerId);

                $selectionCoBorrowerType = Selection::find($coBorrower->selection_type_id);
                $selectionCoBorrowerGender = Selection::find($coBorrower->selection_gender_id);
                $selectionCoBorrowerRace = Selection::find($coBorrower->selection_race_id);
                $selectionCoBorrowerNationality = Selection::find($coBorrower->selection_nationality_id);
                $selectionCoBorrowerEducationLevel = Selection::find($coBorrower->selection_education_level_id);
                $selectionCoBorrowerMarriageStatus = Selection::find($coBorrower->selection_marriage_status_id);

                $loan->loanCustomerProfiles()->create([
                    'customer_id' => $coBorrowerId,
                    'code' => $coBorrower->code,
                    'name' => $coBorrower->name,
                    'email' => $coBorrower->email,
                    'identity_no' => $coBorrower->identity_no,
                    'old_identity_no' => $coBorrower->old_identity_no,
                    'registration_date' => $coBorrower->registration_date,
                    'years_of_incorporation' => $coBorrower->years_of_incorporation,
                    'age' => $coBorrower->age,
                    'birth_date' => $coBorrower->birth_date,
                    'is_primary' => false,
                    'selection_type_id' => $selectionCoBorrowerType?->id,
                    'type' => $selectionCoBorrowerType?->value,
                    'selection_gender_id' => $selectionCoBorrowerGender?->id,
                    'gender' => $selectionCoBorrowerGender?->value,
                    'selection_race_id' => $selectionCoBorrowerRace?->id,
                    'race' => $selectionCoBorrowerRace?->value,
                    'selection_nationality_id' => $selectionCoBorrowerNationality?->id,
                    'nationality' => $selectionCoBorrowerNationality?->value,
                    'selection_education_level_id' => $selectionCoBorrowerEducationLevel?->id,
                    'education_level' => $selectionCoBorrowerEducationLevel?->value,
                    'selection_marriage_status_id' => $selectionCoBorrowerMarriageStatus?->id,
                    'marriage_status' => $selectionCoBorrowerMarriageStatus?->value,
                    'remark' => $coBorrower->remark,
                ]);

                $coBorrowerEmployment = $coBorrower->employment;
                $coBorrowerCompany = $coBorrower->company;

                if ($coBorrower->selection_type_id == 28) {
                    $selectionTermsOfEmployment = Selection::find($coBorrowerEmployment->selection_terms_of_employment_id);
                    $selectionOccupation = Selection::find($coBorrowerEmployment->selection_occupation_id);
                    $selectionBusinessCategory = Selection::find($coBorrowerEmployment->selection_business_category_id);

                    $loanCustomerEmployment = $loanCustomerProfile->loanCustomerEmployment()->create([
                        'employer_name' => $coBorrowerEmployment->employer_name,
                        'length_service_year' => $coBorrowerEmployment->length_service_year,
                        'length_service_month' => $coBorrowerEmployment->length_service_month,
                        'job_position' => $coBorrowerEmployment->job_position,
                        'selection_terms_id' => $selectionTermsOfEmployment?->id,
                        'terms' => $selectionTermsOfEmployment?->value,
                        'selection_occupation_id' => $selectionOccupation?->id,
                        'occupation' => $selectionOccupation?->value,
                        'selection_business_category_id' => $selectionBusinessCategory?->id,
                        'business_category' => $selectionBusinessCategory?->value,
                        'gross_income' => $coBorrowerEmployment->gross_income,
                        'net_income' => $coBorrowerEmployment->net_income,
                    ]);

                    $coBorrowerEmploymentAddress = $coBorrowerEmployment->address;
                    $selectionEmploymentAddressType = Selection::find($coBorrowerEmploymentAddress->selection_type_id);
                    $selectionEmploymentAddressState = Selection::find($coBorrowerEmploymentAddress->selection_state_id);
                    $selectionEmploymentAddressCountry = Selection::find($coBorrowerEmploymentAddress->selection_country_id);

                    $loanCustomerEmployment->address()->create([
                        'category' => $coBorrowerEmploymentAddress->category,
                        'selection_type_id' => $selectionEmploymentAddressType?->id,
                        'type' => $selectionEmploymentAddressType?->value,
                        'line_1' => $coBorrowerEmploymentAddress->line_1,
                        'line_2' => $coBorrowerEmploymentAddress->line_2,
                        'postcode' => $coBorrowerEmploymentAddress->postcode,
                        'city' => $coBorrowerEmploymentAddress->city,
                        'selection_state_id' => $selectionEmploymentAddressState?->id,
                        'state' => $selectionEmploymentAddressState?->value,
                    ]);

                    $coBorrowerEmploymentContacts = $coBorrowerEmployment->contacts;
                    foreach ($coBorrowerEmploymentContacts as $coBorrowerEmploymentContact) {
                        $selectionEmploymentContactType = Selection::find($coBorrowerEmploymentContact->selection_type_id);
                        $selectionEmploymentContactCountry = Selection::find($coBorrowerEmploymentContact->selection_country_id);

                        $loanCustomerEmployment->contacts()->create([
                            'category' => $coBorrowerEmploymentContact->category,
                            'selection_type_id' => $selectionEmploymentContactType?->id,
                            'type' => $selectionEmploymentContactType?->value,
                            'selection_country_id' => $selectionEmploymentContactCountry?->id,
                            'country' => $selectionEmploymentContactCountry?->value,
                            'contact' => $coBorrowerEmploymentContact->contact,
                            'is_primary' => $coBorrowerEmploymentContact->is_primary,
                            'can_receive_sms' => $coBorrowerEmploymentContact->can_receive_sms,
                        ]);
                    }
                } elseif ($coBorrower->selection_type_id == 29) {
                    $selectionNatureOfBusiness = Selection::find($coBorrowerCompany->selection_nature_of_business_id);
                    $selectionCountryOfBusiness = Selection::find($coBorrowerCompany->selection_country_of_business_id);

                    $loanCustomerCompany = $loanCustomerProfile->loanCustomerCompany()->create([
                        'current_paid_up_capital' => $coBorrowerCompany->current_paid_up_capital,
                        'business_turnover' => $coBorrowerCompany->business_turnover,
                        'business_turnover_date' => $coBorrowerCompany->business_turnover_date,
                        'selection_nature_of_business_id' => $selectionNatureOfBusiness?->id,
                        'nature_of_business' => $selectionNatureOfBusiness?->value,
                        'selection_country_of_business_id' => $selectionCountryOfBusiness?->id,
                        'country_of_business' => $selectionCountryOfBusiness?->value,
                    ]);

                    $coBorrowerCompanyOwners = $coBorrowerCompany->owners;
                    foreach ($coBorrowerCompanyOwners as $coBorrowerCompanyOwner) {
                        $selectionOwnerType = Selection::find($coBorrowerCompanyOwner->selection_type_id);
                        $selectionOwnerNationality = Selection::find($coBorrowerCompanyOwner->selection_nationality_id);

                        $loanCustomerCompany->owners()->create([
                            'name' => $coBorrowerCompanyOwner->name,
                            'identity_no' => $coBorrowerCompanyOwner->identity_no,
                            'selection_type_id' => $selectionOwnerType?->id,
                            'type' => $selectionOwnerType?->value,
                            'selection_nationality_id' => $selectionOwnerNationality?->id,
                            'nationality' => $selectionOwnerNationality?->value,
                            'share_unit' => $coBorrowerCompanyOwner->share_unit,
                        ]);
                    }
                }
            }

            // Create collaterals
            $collateralIds = $request->input('collateral.ids', []);
            foreach ($collateralIds as $collateralId) {
                $loanCustomerCollateral = $loan->loanCustomerCollaterals()->create([
                    'customer_collateral_id' => $collateralId,
                ]);

                $customerCollateral = CustomerCollateral::with(
                    'collateral.property.propertyOwners.address', 'collateral.property.propertyOwners.contacts',
                    'collateral.property.address', 'collateral.valuers',
                )->find($collateralId);

                // Duplicate the collateral
                $originalCollateral = $customerCollateral->collateral;

                $selectionCollateralCustomerType = Selection::find($originalCollateral->selection_customer_type_id);
                $selectionCollateralType = Selection::find($originalCollateral->selection_type_id);

                $newCollateral = $originalCollateral->replicate();
                $newCollateral->uuid = null;
                $newCollateral->loan_customer_collateral_id = $loanCustomerCollateral->id;
                $newCollateral->customer_type = $selectionCollateralCustomerType?->value;
                $newCollateral->type = $selectionCollateralType?->value;
                $newCollateral->save();

                // Duplicate the property
                $originalProperty = $customerCollateral->collateral->property;

                $selectionPropertyLandCategory = Selection::find($originalProperty->selection_land_category_id);
                $selectionPropertyTypeOfProperty = Selection::find($originalProperty->selection_type_of_property_id);
                $selectionPropertyLandSizeUnit = Selection::find($originalProperty->selection_land_size_unit);
                $selectionPropertyLandStatus = Selection::find($originalProperty->selection_land_status_id);
                $selectionPropertyBuiltUpAreaUnit = Selection::find($originalProperty->selection_built_up_area_unit);

                $newProperty = $originalProperty->replicate();
                $newProperty->uuid = null;
                $newProperty->collateral_id = $newCollateral->id;
                $newProperty->land_category = $selectionPropertyLandCategory?->value;
                $newProperty->type_of_property = $selectionPropertyTypeOfProperty?->value;
                $newProperty->land_size_unit = $selectionPropertyLandSizeUnit?->value;
                $newProperty->land_status = $selectionPropertyLandStatus?->value;
                $newProperty->built_up_area_unit = $selectionPropertyBuiltUpAreaUnit?->value;
                $newProperty->save();

                // Duplicate the property address
                $originalPropertyAddress = $customerCollateral->collateral->property->address;

                $selectionPropertyAddressType = Selection::find($originalPropertyAddress->selection_type_id);
                $selectionPropertyAddressState = Selection::find($originalPropertyAddress->selection_state_id);
                $selectionPropertyAddressCountry = Selection::find($originalPropertyAddress->selection_country_id);

                $newPropertyAddress = $originalPropertyAddress->replicate();
                $newPropertyAddress->uuid = null;
                $newPropertyAddress->addressable_id = $newProperty->id;
                $newPropertyAddress->addressable_type = CollateralProperty::class;
                $newPropertyAddress->type = $selectionPropertyAddressType?->value;
                $newPropertyAddress->state = $selectionPropertyAddressState?->value;
                $newPropertyAddress->country = $selectionPropertyAddressCountry?->value;
                $newPropertyAddress->save();

                // Duplicate the property owners
                $originalPropertyOwners = $customerCollateral->collateral->property->propertyOwners;
                foreach ($originalPropertyOwners as $originalPropertyOwner) {
                    $newPropertyOwner = $originalPropertyOwner->replicate();
                    $newPropertyOwner->uuid = null;
                    $newPropertyOwner->collateral_property_id = $newProperty->id;
                    $newPropertyOwner->save();

                    // Duplicate the property owner address
                    $originalPropertyOwnerAddress = $originalPropertyOwner->address;

                    $selectionPropertyOwnerAddressType = Selection::find($originalPropertyOwnerAddress->selection_type_id);
                    $selectionPropertyOwnerAddressState = Selection::find($originalPropertyOwnerAddress->selection_state_id);
                    $selectionPropertyOwnerAddressCountry = Selection::find($originalPropertyOwnerAddress->selection_country_id);

                    $newPropertyOwnerAddress = $originalPropertyOwnerAddress->replicate();
                    $newPropertyOwnerAddress->uuid = null;
                    $newPropertyOwnerAddress->addressable_id = $newPropertyOwner->id;
                    $newPropertyOwnerAddress->addressable_type = CollateralPropertyOwner::class;
                    $newPropertyOwnerAddress->type = $selectionPropertyOwnerAddressType?->value;
                    $newPropertyOwnerAddress->state = $selectionPropertyOwnerAddressState?->value;
                    $newPropertyOwnerAddress->country = $selectionPropertyOwnerAddressCountry?->value;
                    $newPropertyOwnerAddress->save();

                    // Duplicate the property owner contacts
                    $originalPropertyOwnerContacts = $originalPropertyOwner->contacts;
                    foreach ($originalPropertyOwnerContacts as $originalPropertyOwnerContact) {
                        $selectionPropertyOwnerContactType = Selection::find($originalPropertyOwnerContact->selection_type_id);
                        $selectionPropertyOwnerContactCountry = Selection::find($originalPropertyOwnerContact->selection_country_id);

                        $newPropertyOwnerContact = $originalPropertyOwnerContact->replicate();
                        $newPropertyOwnerContact->uuid = null;
                        $newPropertyOwnerContact->contactable_id = $newPropertyOwner->id;
                        $newPropertyOwnerContact->contactable_type = CollateralPropertyOwner::class;
                        $newPropertyOwnerContact->type = $selectionPropertyOwnerContactType?->value;
                        $newPropertyOwnerContact->country = $selectionPropertyOwnerContactCountry?->value;
                        $newPropertyOwnerContact->save();
                    }
                }

                // Duplicate the valuers
                $originalValuers = $customerCollateral->collateral->valuers;
                foreach ($originalValuers as $originalValuer) {
                    $newValuer = $originalValuer->replicate();
                    $newValuer->uuid = null;
                    $newValuer->collateral_id = $newCollateral->id;
                    $newValuer->save();
                }
            }

            // Create emergency contact
            $emergency = $request->input('emergency');
            if ($emergency) {
                $emergencyInfo = $loan->loanEmergencies()->create([
                    'name' => $emergency['personal']['name'] ?? null,
                    'identity_no' => $emergency['personal']['identity_no'] ?? null,
                    'birth_date' => $emergency['personal']['birth_date'] ?? null,
                    'age' => $emergency['personal']['age'] ?? null,
                    'selection_gender_id' => $emergency['personal']['selection_gender_id'] ?? null,
                    'selection_relationship_id' => $emergency['personal']['selection_relationship_id'] ?? null,
                    'selection_nationality_id' => $emergency['personal']['selection_nationality_id'] ?? null,
                    'employment_name' => $emergency['employment']['employment_name'] ?? null,
                ]);

                // Personal Information
                $emergencyPersonalDetail = $emergencyInfo->loanEmergencyDetails()->create([
                    'type' => 0,
                ]);

                $emergencyPersonalDetail->address()->create([
                    'selection_state_id' => $emergency['personal']['address']['selection_state_id'] ?? null,
                    'selection_country_id' => $emergency['personal']['address']['selection_country_id'] ?? null,
                    'line_1' => $emergency['personal']['address']['line_1'] ?? null,
                    'line_2' => $emergency['personal']['address']['line_2'] ?? null,
                    'postcode' => $emergency['personal']['address']['postcode'] ?? null,
                    'city' => $emergency['personal']['address']['city'] ?? null,
                    'is_primary' => true,
                ]);

                $emergencyPersonalDetail->contact()->create([
                    'selection_type_id' => $emergency['personal']['contact']['selection_type_id'] ?? null,
                    'category' => $emergency['personal']['contact']['category'] ?? null,
                    'selection_country_id' => $emergency['personal']['contact']['selection_country_id'] ?? null,
                    'contact' => $emergency['personal']['contact']['contact'] ?? null,
                    'can_receive_sms' => $emergency['personal']['contact']['can_receive_sms'] ?? null,
                ]);

                // Employment Information
                $emergencyEmploymentDetail = $emergencyInfo->loanEmergencyDetails()->create([
                    'type' => 1,
                ]);

                $emergencyEmploymentDetail->address()->create([
                    'selection_state_id' => $emergency['employment']['address']['selection_state_id'] ?? null,
                    'selection_country_id' => $emergency['employment']['address']['selection_country_id'] ?? null,
                    'line_1' => $emergency['employment']['address']['line_1'] ?? null,
                    'line_2' => $emergency['employment']['address']['line_2'] ?? null,
                    'postcode' => $emergency['employment']['address']['postcode'] ?? null,
                    'city' => $emergency['employment']['address']['city'] ?? null,
                    'is_primary' => true,
                ]);

                $emergencyEmploymentDetail->contact()->create([
                    'selection_type_id' => $emergency['employment']['contact']['selection_type_id'] ?? null,
                    'category' => $emergency['employment']['contact']['category'] ?? null,
                    'selection_country_id' => $emergency['employment']['contact']['selection_country_id'] ?? null,
                    'contact' => $emergency['employment']['contact']['contact'] ?? null,
                    'can_receive_sms' => $emergency['employment']['contact']['can_receive_sms'] ?? null,
                ]);
            }

            // Create guarantors
            $guarantors = $request->input('guarantor', []);
            foreach ($guarantors as $guarantor) {
                $selectionGender = Selection::find($guarantor['personal']['selection_gender_id']);
                $selectionRelationship = Selection::find($guarantor['personal']['selection_relationship_id']);
                $selectionNationality = Selection::find($guarantor['personal']['selection_nationality_id']);
                $selectionTermsOfEmployment = Selection::find($guarantor['employment']['selection_terms_of_employment_id']);
                $selectionOccupation = Selection::find($guarantor['employment']['selection_occupation_id']);
                $selectionBusinessClassification = Selection::find($guarantor['employment']['selection_business_classification_id']);

                $loanGuarantor = $loan->loanGuarantors()->create([
                    'name' => $guarantor['personal']['name'] ?? null,
                    'identity_no' => $guarantor['personal']['identity_no'] ?? null,
                    'birth_date' => $guarantor['personal']['birth_date'] ?? null,
                    'age' => $guarantor['personal']['age'] ?? null,
                    'selection_gender_id' => $selectionGender?->id,
                    'gender' => $selectionGender?->value,
                    'selection_relationship_id' => $selectionRelationship?->id,
                    'relationship' => $selectionRelationship?->value,
                    'selection_nationality_id' => $selectionNationality?->id,
                    'nationality' => $selectionNationality?->value,
                    'employment_name' => $guarantor['employment']['employment_name'] ?? null,
                    'length_service_year' => $guarantor['employment']['length_service_year'] ?? null,
                    'length_service_month' => $guarantor['employment']['length_service_month'] ?? null,
                    'job_position' => $guarantor['employment']['job_position'] ?? null,
                    'selection_terms_of_employment_id' => $selectionTermsOfEmployment?->id,
                    'terms_of_employment' => $selectionTermsOfEmployment?->value,
                    'selection_occupation_id' => $selectionOccupation?->id,
                    'occupation' => $selectionOccupation?->value,
                    'selection_business_classification_id' => $selectionBusinessClassification?->id,
                    'business_classification' => $selectionBusinessClassification?->value,
                ]);

                // Personal Information
                $loanGuarantorPersonalDetail = $loanGuarantor->loanGuarantorDetails()->create([
                    'type' => 0,
                ]);

                $loanGuarantorPersonalDetail->address()->create([
                    'selection_state_id' => $guarantor['personal']['address']['selection_state_id'] ?? null,
                    'selection_country_id' => $guarantor['personal']['address']['selection_country_id'] ?? null,
                    'line_1' => $guarantor['personal']['address']['line_1'] ?? null,
                    'line_2' => $guarantor['personal']['address']['line_2'] ?? null,
                    'postcode' => $guarantor['personal']['address']['postcode'] ?? null,
                    'city' => $guarantor['personal']['address']['city'] ?? null,
                    'is_primary' => true,
                ]);

                $loanGuarantorPersonalDetail->contact()->create([
                    'selection_type_id' => $guarantor['personal']['contact']['selection_type_id'] ?? null,
                    'category' => $guarantor['personal']['contact']['category'] ?? null,
                    'selection_country_id' => $guarantor['personal']['contact']['selection_country_id'] ?? null,
                    'contact' => $guarantor['personal']['contact']['contact'] ?? null,
                    'can_receive_sms' => $guarantor['personal']['contact']['can_receive_sms'] ?? null,
                ]);

                // Employment Information
                $loanGuarantorEmploymentDetail = $loanGuarantor->loanGuarantorDetails()->create([
                    'type' => 1,
                ]);

                $loanGuarantorEmploymentDetail->address()->create([
                    'selection_state_id' => $guarantor['employment']['address']['selection_state_id'] ?? null,
                    'selection_country_id' => $guarantor['employment']['address']['selection_country_id'] ?? null,
                    'line_1' => $guarantor['employment']['address']['line_1'] ?? null,
                    'line_2' => $guarantor['employment']['address']['line_2'] ?? null,
                    'postcode' => $guarantor['employment']['address']['postcode'] ?? null,
                    'city' => $guarantor['employment']['address']['city'] ?? null,
                    'is_primary' => true,
                ]);

                $loanGuarantorEmploymentDetail->contact()->create([
                    'selection_type_id' => $guarantor['employment']['contact']['selection_type_id'] ?? null,
                    'category' => $guarantor['employment']['contact']['category'] ?? null,
                    'selection_country_id' => $guarantor['employment']['contact']['selection_country_id'] ?? null,
                    'contact' => $guarantor['employment']['contact']['contact'] ?? null,
                    'can_receive_sms' => $guarantor['employment']['contact']['can_receive_sms'] ?? null,
                ]);
            }

            // Create documents
            $documentIds = $request->input('document.ids', []);
            foreach ($documentIds as $documentId) {
                $document = CustomerDocument::find($documentId);
                $selectionDocumentType = Selection::find($document->selection_type_id);

                $loanDocument = $loan->loanDocuments()->create([
                    'selection_type_id' => $selectionDocumentType?->id,
                    'type' => $selectionDocumentType?->value,
                ]);

                $loanDocument->documents()->create([
                    'category' => $document->category,
                    'url' => $document->url,
                ]);
            }

            DB::commit();

            return Redirect::route('loans.index')->with('success', 'Loan created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());

            return Redirect::back()->with('error', 'Failed to create loan: '.$e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified loan.
     */
    public function show(Loan $loan): Response
    {
        $this->authorize('view', $loan);

        $loan->withAuditUsers();
        $loan->load([
            'client:id,code,name,email',
            'company:id,code,display_name',
            'team:id,name',
            'agent:id,username',
            'selectionType:id,value',
        ]);

        return Inertia::render('loans/Show', [
            'loan' => (new LoanResource($loan))->toArray(request(), true),
        ]);
    }

    /**
     * Show the form for editing the specified loan.
     */
    public function edit(Loan $loan): Response
    {
        $this->authorize('update', $loan);

        return Inertia::render('loans/Edit', [
            'loan' => $loan,
        ]);
    }

    /**
     * Update the specified loan in storage.
     */
    public function update(UpdateLoanRequest $request, Loan $loan): RedirectResponse
    {
        $this->authorize('update', $loan);

        return Redirect::route('loans.index')->with('success', 'Loan updated successfully.');
    }

    /**
     * Remove the specified loan from storage.
     */
    public function destroy(Loan $loan): RedirectResponse
    {
        $this->authorize('delete', $loan);

        return Redirect::route('loans.index')->with('success', 'Loan deleted successfully.');
    }

    public function loanApi(Request $request)
    {
        $search = $request->search;
        $matches = [];
        $api = [
            [
                'value' => 'backlog',
                'label' => 'Backlog',
            ],
            [
                'value' => 'todo',
                'label' => 'Todo',
            ],
            [
                'value' => 'in progress',
                'label' => 'In Progress',
            ],
            [
                'value' => 'done',
                'label' => 'Done',
            ],
            [
                'value' => 'canceled',
                'label' => 'Canceled',
            ],
        ];

        foreach ($api as $item) {
            if (stripos($item['label'], $search) !== false) {
                $matches[] = $item;
            }
        }

        return $matches;
    }
}
