<?php

namespace App\Http\Requests\Users;

use App\Http\Requests\BaseRequest;
use App\Models\Role;
use Illuminate\Validation\Rules;

class StoreUserRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $baseRules = [
            'username' => 'required|string|max:255|unique:users',
            'email' => 'nullable|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => 'required|integer|exists:roles,id',
            'status' => 'required|integer',
        ];

        $organizationRules = [
            'headquarter_id' => 'nullable|exists:companies,id',
            'company_id' => 'nullable|exists:companies,id',
            'team_id' => 'nullable|exists:teams,id',
        ];

        if ($this->filled('role')) {
            $role = Role::find($this->input('role'));

            if ($role) {
                if ($role->is_required_headquarter) {
                    $organizationRules['headquarter_id'] = 'required|exists:companies,id';
                }

                if ($role->is_required_company) {
                    $organizationRules['company_id'] = 'required|exists:companies,id';
                }

                if ($role->is_required_team) {
                    $organizationRules['team_id'] = 'required|exists:teams,id';
                }
            }
        }

        return array_merge($baseRules, $organizationRules);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'username.required' => 'The username is required.',
            'username.unique' => 'This username is already in use.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email is already in use.',
            'password.required' => 'The password is required.',
            'password.confirmed' => 'The password confirmation does not match.',
            'role.required' => 'Please select a role for this user.',
            'role.exists' => 'The selected role does not exist.',
            'status.required' => 'Please select a status for this user.',
            'headquarter_id.exists' => 'The selected headquarter does not exist.',
            'company_id.exists' => 'The selected company does not exist.',
            'team_id.exists' => 'The selected team does not exist.',
        ];
    }
}
