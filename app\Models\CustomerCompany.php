<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * CustomerCompany model for managing customer company information
 */
class CustomerCompany extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'customer_id',
        'current_paid_up_capital',
        'business_turnover',
        'business_turnover_date',
        'business_net_income',
        'business_net_income_date',
        'selection_nature_of_business_id',
        'selection_country_of_business_id',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'customer_id' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the customer that owns the address.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(CustomerProfile::class, 'customer_id');
    }

    public function owners(): MorphMany
    {
        return $this->morphMany(Owner::class, 'ownerable');
    }

    /**
     * Get the selection for nature of business.
     */
    public function selectionNatureOfBusiness(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_nature_of_business_id');
    }

    /**
     * Get the selection for country of business.
     */
    public function selectionCountryOfBusiness(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_country_of_business_id');
    }
}
