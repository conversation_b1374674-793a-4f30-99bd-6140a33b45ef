<?php

namespace App\Models;

use App\Enums\AccessControl\RoleName;
use App\Enums\Team\TeamStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

/**
 * Team model for managing teams
 */
class Team extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'company_id',
        'code',
        'name',
        'email',
        'website',
        'contact_no',
        'status',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'status' => TeamStatus::class,
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the admin profiles associated with this team.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the AdminProfile class, allowing this team to be associated
     * with various admin profiles through the 'teamable' polymorphic relation.
     */
    public function adminProfiles(): MorphToMany
    {
        return $this->morphedByMany(AdminProfile::class, 'teamable');
    }

    /**
     * Get the agent profiles associated with this team.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the AgentProfile class, allowing this team to be associated
     * with various agent profiles through the 'teamable' polymorphic relation.
     */
    public function agentProfiles(): MorphToMany
    {
        return $this->morphedByMany(AgentProfile::class, 'teamable');
    }

    /**
     * Get the customer profiles associated with this team.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the CustomerProfile class, allowing this team to be associated
     * with various customer profiles through the 'teamable' polymorphic relation.
     */
    public function customerProfiles(): MorphToMany
    {
        return $this->morphedByMany(CustomerProfile::class, 'teamable');
    }

    /**
     * Get the headquarters associated with this team.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the Headquarter class, allowing this team to be associated
     * with various headquarters through the 'teamable' polymorphic relation.
     */
    public function headquarters(): MorphToMany
    {
        return $this->morphedByMany(Headquarter::class, 'teamable');
    }

    /**
     * Get the companies associated with this team.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the Company class, allowing this team to be associated
     * with various companies through the 'teamable' polymorphic relation.
     */
    public function companies(): MorphToMany
    {
        return $this->morphedByMany(Company::class, 'teamable');
    }

    /**
     * Get the address for this team.
     */
    public function address(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    /**
     * Get the loans associated with this team.
     */
    public function loans(): HasMany
    {
        return $this->hasMany(Loan::class, 'team_id');
    }

    /**
     * Get the contacts associated with this team.
     */
    public function contacts(): MorphOne
    {
        return $this->morphOne(Contact::class, 'contactable');
    }

    /**
     * Get the telephone contact for this team.
     */
    public function telephone(): ?Contact
    {
        return $this->contacts()->where('category', Contact::CATEGORY_TELEPHONE)->first();
    }

    /**
     * Scope a query to get teams for dropdown lists.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDropdown($query)
    {
        return $query->select('id', 'name')
            ->orderBy('name');
    }

    /**
     * Get teams with company information for dropdown selection
     *
     * @param  array  $columns  Additional columns to select
     * @param  bool  $filterByUser  Whether to filter by user permissions
     * @return array Formatted team data for frontend
     */
    public static function getForDropdown(array $columns = [], bool $filterByUser = true): array
    {
        $defaultColumns = ['id', 'name'];
        $selectColumns = $columns ? array_merge($defaultColumns, $columns) : $defaultColumns;

        $query = self::forDropdown()->select($selectColumns);

        if ($filterByUser && ! auth()->user()->hasRole(RoleName::SUPER_ADMIN)) {
            $userTeamIds = auth()->user()->adminProfiles
                ->flatMap(function ($profile) {
                    $teamIds = $profile->teams()->pluck('id');

                    $headquarterIds = Team::whereIn('id', $teamIds)->get()
                        ->flatMap(function ($team) {
                            return $team->companies()
                                ->pluck('headquarter_id');
                        });

                    if ($headquarterIds->isNotEmpty()) {
                        $companyIds = Company::whereIn('headquarter_id', $headquarterIds)->pluck('id');

                        $companyTeamIds = Company::whereIn('id', $companyIds)
                            ->get()
                            ->flatMap(function ($company) {
                                return $company->teams()->pluck('id');
                            });

                        $teamIds = $teamIds->merge($companyTeamIds);
                    }

                    if ($profile->headquarters()->exists() || $profile->companies()->exists()) {
                        $companyIds = collect();

                        if ($profile->headquarters()->exists()) {
                            $companyIds = $companyIds->merge(
                                Company::whereIn('headquarter_id', $profile->headquarters()->pluck('id'))->pluck('id')
                            );
                        }

                        if ($profile->companies()->exists()) {
                            $companyIds = $companyIds->merge($profile->companies()->pluck('id'));
                        }

                        $companyTeamIds = Company::whereIn('id', $companyIds->unique())
                            ->get()
                            ->flatMap(function ($company) {
                                return $company->teams()->pluck('id');
                            });

                        $teamIds = $teamIds->merge($companyTeamIds);
                    }

                    return $teamIds;
                })
                ->unique();

            $query->whereIn('id', $userTeamIds);
        }

        return $query->get()
            ->map(fn ($team) => [
                'id' => $team->id,
                'name' => $team->name,
                'company_id' => optional($team->companies()->first())->id,
                'company_name' => optional($team->companies()->first())->display_name,
            ])
            ->toArray();
    }

    /**
     * Get headquarters accessible to the specified user.
     *
     * @param  \App\Models\User|null  $user
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function forUser($user = null)
    {
        $user = $user ?? auth()->user();

        if ($user->hasRole(RoleName::SUPER_ADMIN)) {
            return self::query();
        }

        $userCompanyIds = $user->adminProfiles
            ->flatMap(function ($profile) {
                $companyIds = collect();

                if ($profile->headquarters()->exists()) {
                    $companyIds = $companyIds->merge(
                        Company::whereIn('headquarter_id', $profile->headquarters()->pluck('id'))->pluck('id')
                    );
                }

                $companyIds = $companyIds->merge($profile->companies()->pluck('id'));

                $teamCompanyIds = $profile->teams()
                    ->get()
                    ->flatMap(function ($team) {
                        $companyIds = $team->companies()->pluck('id');

                        $headquarterIds = $team->companies()
                            ->where('is_headquarter', true)
                            ->pluck('id');

                        if ($headquarterIds->isNotEmpty()) {
                            $companyIds = $companyIds->merge(
                                Company::whereIn('headquarter_id', $headquarterIds)->pluck('id')
                            );
                        }

                        return $companyIds;
                    });

                return $companyIds->merge($teamCompanyIds);
            })
            ->unique();

        return self::whereIn('company_id', $userCompanyIds);
    }
}
