<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/composables/useAuth';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { formatEnumOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();
const { hasHeadquarterAccess, hasCompanyAccess } = useAuth();

interface Team {
    id: number;
    uuid: string;
    name: string;
    status: number;
    company: {
        id: number;
        display_name: string;
    } | null;
    headquarter: {
        id: number;
        display_name: string;
    } | null;
    address: {
        line_1: string | null;
    } | null;
    updated_at: string;
    updated_by: {
        id: number;
        name: string;
    } | null;
}

interface Props {
    teams: PaginatedData<Team>;
    filters: FilterOptions & {
        name?: string;
        company_name?: string;
        status?: number;
    };
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({});

const searchValue = ref(props.filters.name || '');
const companyName = ref(props.filters.company_name || '');
const teamStatus = ref(props.filters.status || '');

const debouncedSearch = useDebounceFn(() => {
    form.get(
        route('teams.index', {
            name: searchValue.value,
            company_name: companyName.value,
            status: teamStatus.value,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
}, 300);

const handleSearch = () => {
    debouncedSearch();
};

const handleReset = () => {
    searchValue.value = '';
    companyName.value = '';
    teamStatus.value = '';
    form.get(
        route('teams.index', {
            ...props.filters,
            name: '',
            company_name: '',
            status: '',
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const handleView = (team: Team) => {
    form.get(route('teams.show', team.id));
};

const handleEdit = (team: Team) => {
    form.get(route('teams.edit', team.id));
};

const handleToggleStatus = (data: { row: Team; newStatus: number }) => {
    const { row: team, newStatus } = data;
    updateStatus(team, newStatus);
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';
    form.get(
        route('teams.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const updateStatus = (team: Team, newStatus: number) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Do you want to change the status of ${team.name} to ${newStatus === 0 ? 'Active' : 'Inactive'}?`,
        },
        submitOptions: {
            method: 'put',
            url: route('teams.update-status', team.id),
            transform: (data) => ({
                ...data,
                status: newStatus,
            }),
            successMessage: `Status of ${team.name} has been updated.`,
            errorMessage: `Unable to update status for ${team.name}. Please try again.`,
            entityName: 'team',
        },
    });
};

const columns = computed(() => {
    const cols = [{ field: 'name', label: 'Team Name', sortable: true, width: 'w-40' }];

    if (!hasHeadquarterAccess.value) {
        cols.push({ field: 'headquarter.display_name', label: 'Headquarter Name', sortable: true });
    }

    if (!hasCompanyAccess.value) {
        cols.push({ field: 'company.display_name', label: 'Company Name', sortable: true });
    }

    return [
        ...cols,
        { field: 'address.line_1', label: 'Address', sortable: true },
        { field: 'updated_at', label: 'Updated At', sortable: true, width: 'w-40', format: (value) => formatDateTime(value) },
        { field: 'updated_by.name', label: 'Updated By', sortable: true },
        { field: 'status', label: 'Status', sortable: true, width: 'w-30' },
        { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
    ];
});

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Teams" />

        <div class="px-4 py-3">
            <Heading title="Team" pageNumber="P000009" />
            <SearchCard
                v-model:searchValue="searchValue"
                searchLabel="Team Name"
                searchPlaceholder="Team Name"
                @search="handleSearch"
                @reset="handleReset"
            >
                <template #additional-filters>
                    <div class="w-full" v-if="!hasCompanyAccess">
                        <Label class="pb-2" for="company-name">Company Name</Label>
                        <Input id="company-name" placeholder="Company Name" v-model="companyName" class="w-full" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="status">Status</Label>
                        <Select :model-value="teamStatus" @update:model-value="teamStatus = Number($event)">
                            <SelectTrigger class="w-full">
                                <SelectValue placeholder="Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="option in formatEnumOptions(props.statuses)" :key="option.value" :value="option.value">
                                    {{ option.label }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </template>
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('teams.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Team
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="teams.data"
                        :sort-state="sortState"
                        empty-message="No teams found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        @toggleStatus="handleToggleStatus"
                        :showDeleteButton="false"
                    >
                        <!-- Custom cell renderers -->
                        <template #cell-status="{ row }">
                            <Badge
                                :class="[
                                    {
                                        'bg-green': row.status === 0,
                                        'bg-canary': row.status === 1,
                                    },
                                    'text-md w-15 px-1 py-0',
                                ]"
                            >
                                {{ row.status === 0 ? 'Active' : 'Inactive' }}
                            </Badge>
                        </template>
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="teams.from" :to="teams.to" :total="teams.total" entityName="teams" />
                            </div>
                            <Pagination :links="teams.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
