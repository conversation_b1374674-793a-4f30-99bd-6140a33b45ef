<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

interface Selection {
    id: number;
    uuid: string;
    category: string;
    value: string;
    description: string | null;
    sort_order: number;
    status: number;
    created_at: string;
    updated_at: string;
    created_by: {
        id: number;
        username: string;
    } | null;
    updated_by: {
        id: number;
        username: string;
    } | null;
}

interface Props {
    selection: Selection;
}

const props = defineProps<Props>();
const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`Selection - ${selection.value}`" />

        <div class="px-4 py-3">
            <Heading title="Selection" pageNumber="P000028" description="View details of the selection record" />

            <AppCard title="View Selection" backRoute="selections.index" :form="form" :itemId="props.selection.id">
                <dl class="grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Category</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.selection.category }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Value</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.selection.value }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Description</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.selection.description || '-' }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Sort Order</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.selection.sort_order }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Status</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.selection.status === 0 ? 'Active' : 'Inactive' }}
                        </dd>
                    </div>
                </dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
