<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useAuth } from '@/composables/useAuth';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

const { submitWithConfirmation } = useFormSubmit();
const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess } = useAuth();

interface Props {
    outcomeType: {
        id: number;
        name: string;
        company: string;
        headquarter: string;
    };
}

const props = defineProps<Props>();

const form = useForm({
    name: props.outcomeType.name,
    headquarter: props.outcomeType.headquarter,
    company: props.outcomeType.company,
});

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('agent-outcome-types.update', props.outcomeType.id),
            entityName: 'outcome type',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Edit Agent Outcome Type" />

        <div class="px-4 py-3">
            <Heading title="Agent Outcome Types" pageNumber="P000034" description="Edit the selected agent outcome type record" />

            <AppCard title="Edit Agent Outcome Type" :form="form" backRoute="agent-outcome-types.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Agent Outcome Type -->
                            <FormField
                                id="name"
                                label="Outcome Type"
                                v-model="form.name"
                                type="input"
                                required
                                placeholder="Outcome Type"
                                :error="form.errors.name"
                            />

                            <!-- Headquarter Name -->
                            <FormField
                                id="display_name"
                                label="Headquarter Name"
                                v-model="form.headquarter"
                                type="show"
                                placeholder="Headquarter Name"
                                v-if="!hasHeadquarterAccess"
                            />

                            <!-- Company Name -->
                            <FormField
                                id="display_name"
                                label="Company Name"
                                v-model="form.company"
                                type="show"
                                placeholder="Company Name"
                                v-if="!hasCompanyAccess && !isHeadquarter"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
