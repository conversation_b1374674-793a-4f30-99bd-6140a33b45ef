<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/composables/useAuth';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess } = useAuth();

interface Company {
    display_name: string;
}

interface OutcomeType {
    id: number;
    uuid: string;
    name: string;
    headquarter: Company[];
    company: Company[];
}

interface Props {
    outcomeType: OutcomeType;
}

const props = defineProps<Props>();
const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`Agent Outcome Type: ${outcomeType.name}`" />
        <div class="px-4 py-3">
            <Heading title="Agent Outcome Type" pageNumber="P000035" description="View details of the agent outcome type record" />

            <AppCard title="View Agent Outcome Type" backRoute="agent-outcome-types.index" :form="form" :itemId="props.outcomeType.id">
                <dl class="grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Outcome Type Name</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.outcomeType.name }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3" v-if="!hasHeadquarterAccess">
                        <Label class="font-medium">Headquarter Name</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.outcomeType.headquarter.display_name }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3" v-if="!hasCompanyAccess && !isHeadquarter">
                        <Label class="font-medium">Company Name</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.outcomeType.company.display_name }}
                        </dd>
                    </div>
                </dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
