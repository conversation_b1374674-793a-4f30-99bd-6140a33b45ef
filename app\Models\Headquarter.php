<?php

namespace App\Models;

use App\Enums\AccessControl\RoleName;
use App\Enums\Company\HeadquarterStatus;
use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

/**
 * Headquarter model representing both headquarters and branch companies
 */
class Headquarter extends BaseModel
{
    use HasFactory, UniqueCodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'code',
        'name',
        'display_name',
        'status',
        'remark',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => HeadquarterStatus::class,
    ];

    /**
     * Set the display name and automatically set the name to uppercase.
     *
     * @param  string  $value
     * @return void
     */
    public function setDisplayNameAttribute($value)
    {
        $this->attributes['display_name'] = $value;
        $this->attributes['name'] = strtoupper($value);
    }

    /**
     * Scope a query to only include headquarters.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeHeadquarterOnly($query)
    {
        return $query->whereNull('companies.headquarter_id');
    }

    /**
     * Scope a query to only include branch companies.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompanyOnly($query)
    {
        return $query->whereNotNull('companies.headquarter_id');
    }

    /**
     * Scope a query to get companies for dropdown lists.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDropdown($query)
    {
        return $query->select('id', 'display_name', 'headquarter_id')
            ->orderBy('display_name');
    }

    /**
     * Get the admin profiles associated with this company.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the AdminProfile class, allowing this company to be associated
     * with various admin profiles through the 'headquarterables' polymorphic relation.
     */
    public function adminProfiles(): MorphToMany
    {
        return $this->morphedByMany(AdminProfile::class, 'headquarterables');
    }

    /**
     * Get the teams associated with this headquarter.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the Team class, allowing this headquarter to be associated
     * with various teams through the 'teamable' polymorphic relation.
     */
    public function teams(): MorphToMany
    {
        return $this->morphToMany(Team::class, 'teamable');
    }

    /**
     * Get the parent company of this company.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'headquarter_id');
    }

    /**
     * Get the child companies of this company.
     */
    public function children(): HasMany
    {
        return $this->hasMany(Company::class, 'headquarter_id');
    }

    /**
     * Get the companies associated with this headquarter.
     */
    public function companies(): HasMany
    {
        return $this->hasMany(Company::class, 'headquarter_id');
    }

    /**
     * Get the headquarter company associated with this headquarter.
     */
    public function headquarterCompany(): HasOne
    {
        return $this->hasOne(Company::class, 'headquarter_id')
            ->where('is_headquarter', true)
            ->withDefault();
    }

    /**
     * Get headquarters for dropdown selection
     *
     * @param  array  $columns  Additional columns to select
     * @param  bool  $filterByUser  Whether to filter by user permissions
     * @return array Formatted headquarters data
     */
    public static function getForDropdown(array $columns = [], bool $filterByUser = true): array
    {
        $defaultColumns = ['id', 'display_name', 'code'];
        $selectColumns = $columns ? array_merge($defaultColumns, $columns) : $defaultColumns;

        $query = self::forDropdown()->select($selectColumns);

        if ($filterByUser && ! auth()->user()->hasRole(RoleName::SUPER_ADMIN)) {
            $user = auth()->user();

            $userHeadquarterIds = $user->adminProfiles
                ->flatMap(function ($profile) {
                    $hqIds = collect();

                    $hqIds = $hqIds->merge($profile->headquarters()->pluck('id'));

                    $hqIds = $hqIds->merge(
                        $profile->companies()
                            ->whereNotNull('headquarter_id')
                            ->pluck('headquarter_id')
                    );

                    $teamHqIds = $profile->teams()->get()->flatMap(function ($team) {
                        $directHqIds = $team->headquarters()->pluck('id');

                        if ($directHqIds->isEmpty()) {
                            return $team->companies()
                                ->whereNull('headquarter_id')
                                ->pluck('id')
                                ->merge(
                                    $team->companies()
                                        ->whereNotNull('headquarter_id')
                                        ->pluck('headquarter_id')
                                );
                        }

                        return $directHqIds;
                    });

                    return $hqIds->merge($teamHqIds);
                })
                ->unique();

            $query->whereIn('id', $userHeadquarterIds);
        }

        return $query->get()
            ->map(fn ($headquarter) => [
                'id' => $headquarter->id,
                'display_name' => $headquarter->display_name,
                'code' => $headquarter->code,
                'headquarter_id' => null,
            ])
            ->toArray();
    }

    /**
     * The "boot" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate code if not provided
        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new headquarter.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix('HQ');
    }

    /**
     * Get headquarters accessible to the specified user.
     *
     * @param  \App\Models\User|null  $user
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function forUser($user = null)
    {
        $user = $user ?? auth()->user();

        if ($user->hasRole(RoleName::SUPER_ADMIN)) {
            return self::query();
        }

        $userHeadquarterIds = $user->adminProfiles
            ->flatMap(fn ($profile) => $profile->headquarters()->pluck('id'))
            ->unique();

        return self::whereIn('id', $userHeadquarterIds);
    }

    /**
     * Check if the given user has access to this headquarter
     *
     * @param  \App\Models\User|null  $user
     */
    public function isAccessibleBy($user = null): bool
    {
        $user = $user ?? auth()->user();

        if ($user->hasRole(RoleName::SUPER_ADMIN)) {
            return true;
        }

        $accessibleIds = self::forUser($user)->pluck('id')->toArray();

        return in_array($this->id, $accessibleIds);
    }
}
