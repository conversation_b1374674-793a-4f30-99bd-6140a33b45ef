<?php

namespace App\Models;

use App\Enums\AccessControl\RoleName;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * AgentOutcome model for managing agent outcome
 */
class AgentOutcome extends BaseModel
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'company_id',
        'agent_id',
        'outcome_types_id',
        'outcome_types',
        'amount',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'agent_id' => 'integer',
            'outcome_types_id' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the companies associated with this agent outcome type.
     */
    public function companies(): MorphToMany
    {
        return $this->morphToMany(Company::class, 'companyable');
    }

    /**
     * Get the agent outcome type information.
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(AgentProfile::class, 'agent_id');
    }

    /**
     * Get the agent outcome type information.
     */
    public function outcomeType(): BelongsTo
    {
        return $this->belongsTo(AgentOutcomeType::class, 'outcome_types_id');
    }

    /**
     * Get the user's resolve hierarchy.
     */
    public function getResolveHierarchy(): array
    {
        $agentOutcomeProfile = $this;

        if (! $this) {
            return ['headquarter' => null, 'company' => null];
        }

        if ($directCompany = $agentOutcomeProfile->companies->first()) {
            $headquarter = ($directCompany->headquarter_id && $directCompany->parent)
                ? $directCompany->parent
                : null;

            return [
                'headquarter' => $headquarter,
                'company' => $directCompany,
            ];
        }

        return [
            'headquarter' => null,
            'company' => null,
        ];
    }

    /**
     * Get headquarters accessible to the specified user.
     *
     * @param  \App\Models\User|null  $user
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function forUser($user = null)
    {
        $user = $user ?? auth()->user();

        if ($user->hasRole(RoleName::SUPER_ADMIN)) {
            return self::query();
        }

        $userCompanyIds = $user->adminProfiles
            ->flatMap(function ($profile) {
                $companyIds = collect();

                if ($profile->headquarters()->exists()) {
                    $companyIds = $companyIds->merge(
                        Company::whereIn('headquarter_id', $profile->headquarters()->pluck('id'))->pluck('id')
                    );
                }

                $companyIds = $companyIds->merge($profile->companies()->pluck('id'));

                $teamCompanyIds = $profile->teams()
                    ->get()
                    ->flatMap(function ($team) {
                        $companyIds = $team->companies()->pluck('id');

                        $headquarterIds = $team->companies()
                            ->where('is_headquarter', true)
                            ->pluck('id');

                        if ($headquarterIds->isNotEmpty()) {
                            $companyIds = $companyIds->merge(
                                Company::whereIn('headquarter_id', $headquarterIds)->pluck('id')
                            );
                        }

                        return $companyIds;
                    });

                return $companyIds->merge($teamCompanyIds);
            })
            ->unique();

        return self::whereIn('company_id', $userCompanyIds);
    }
}
