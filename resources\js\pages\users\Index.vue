<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/composables/useAuth';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { formatEnumOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();
const { hasHeadquarterAccess } = useAuth();

interface User {
    id: number;
    uuid: string;
    code: string | null;
    username: string;
    email: string | null;
    status: number;
    roles: {
        id: number;
        name: string;
    }[];
    headquarter: {
        id: number;
        display_name: string;
        code: string;
    } | null;
    company: {
        id: number;
        display_name: string;
        code: string;
    } | null;
    team: {
        id: number;
        name: string;
    } | null;
    updated_at: string;
    updated_by: {
        id: number;
        name: string;
    } | null;
}

interface Props {
    users: PaginatedData<User>;
    filters: FilterOptions & {
        username?: string;
        role?: string;
        headquarter_name?: string;
        company_name?: string;
        team_name?: string;
        status?: number;
    };
    availableRoles: string[];
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({});

const userName = ref(props.filters.username || '');
const role = ref(props.filters.role || '');
const headquarterName = ref(props.filters.headquarter_name || '');
const companyName = ref(props.filters.company_name || '');
const teamName = ref(props.filters.team_name || '');
const userStatus = ref(props.filters.status || '');

const debouncedSearch = useDebounceFn(() => {
    form.get(
        route('users.index', {
            username: userName.value,
            role: role.value,
            headquarter_name: headquarterName.value,
            company_name: companyName.value,
            team_name: teamName.value,
            status: userStatus.value,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
}, 300);

const handleSearch = () => {
    debouncedSearch();
};

const handleReset = () => {
    userName.value = '';
    role.value = '';
    headquarterName.value = '';
    companyName.value = '';
    teamName.value = '';
    userStatus.value = '';

    form.get(route('users.index'));
};

const handleView = (user: User) => {
    form.get(route('users.show', user.id));
};

const handleEdit = (user: User) => {
    form.get(route('users.edit', user.id));
};

const handleToggleStatus = (data: { row: User; newStatus: number }) => {
    const { row: user, newStatus } = data;
    updateStatus(user, newStatus);
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';
    form.get(
        route('users.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const updateStatus = (user: User, newStatus: number) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Do you want to change the status of ${user.username} to ${newStatus === 1 ? 'Active' : 'Inactive'}?`,
        },
        submitOptions: {
            method: 'put',
            url: route('users.update-status', user.id),
            transform: (data) => ({
                ...data,
                status: newStatus,
            }),
            successMessage: `Status of ${user.username} has been updated.`,
            errorMessage: `Unable to update status for ${user.username}. Please try again.`,
            entityName: 'user',
        },
    });
};

const columns = computed(() => {
    const cols = [
        { field: 'code', label: 'Code', sortable: false, width: 'w-40' },
        { field: 'username', label: 'Username', sortable: true },
    ];

    if (!hasHeadquarterAccess.value) {
        cols.push({ field: 'headquarter.display_name', label: 'Headquarter Name', sortable: true });
    }

    return [
        ...cols,
        { field: 'company.display_name', label: 'Company Name', sortable: true },
        { field: 'team.name', label: 'Team Name', sortable: true },
        {
            field: 'roles',
            label: 'Role',
            sortable: false,
            format: (roles: { id: number; name: string }[]) => roles.map((role) => role.name).join(', '),
        },
        { field: 'email', label: 'Email', sortable: true },
        { field: 'updated_at', label: 'Updated At', sortable: true, width: 'w-40', format: (value) => formatDateTime(value) },
        { field: 'updated_by.name', label: 'Updated By', sortable: true },
        { field: 'status', label: 'Status', sortable: true, width: 'w-30' },
        { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
    ];
});

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Users" />

        <div class="px-4 py-3">
            <Heading title="User" pageNumber="P000013" />

            <SearchCard
                v-model:searchValue="userName"
                searchLabel="Username"
                searchPlaceholder="Username"
                @search="handleSearch"
                @reset="handleReset"
            >
                <template #additional-filters>
                    <div class="w-full" v-if="!hasHeadquarterAccess">
                        <Label class="pb-2" for="headquarter-name">Headquarter Name</Label>
                        <Input id="headquarter-name" placeholder="Headquarter Name" v-model="headquarterName" class="w-full" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="company-name">Comapany Name</Label>
                        <Input id="company-name" placeholder="Company Name" v-model="companyName" class="w-full" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="team-name">Team Name</Label>
                        <Input id="team-name" placeholder="Team Name" v-model="teamName" class="w-full" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="role">Role</Label>
                        <Select :model-value="role" @update:model-value="role = String($event)">
                            <SelectTrigger class="w-full">
                                <SelectValue placeholder="Role" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="option in availableRoles" :key="option" :value="option">
                                    {{ option }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="status">Status</Label>
                        <Select :model-value="userStatus" @update:model-value="userStatus = Number($event)">
                            <SelectTrigger class="w-full">
                                <SelectValue placeholder="Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="option in formatEnumOptions(props.statuses)" :key="option.value" :value="option.value">
                                    {{ option.label }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </template>
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('users.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New User
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="users.data"
                        :sort-state="sortState"
                        empty-message="No users found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        @toggleStatus="handleToggleStatus"
                        :showDeleteButton="false"
                    >
                        <!-- Custom cell renderers -->
                        <template #cell-status="{ row }">
                            <Badge
                                :class="[
                                    {
                                        'bg-green': row.status === 0,
                                        'bg-canary': row.status === 1,
                                    },
                                    'text-md w-15 px-1 py-0',
                                ]"
                            >
                                {{ row.status === 0 ? 'Active' : 'Inactive' }}
                            </Badge>
                        </template>
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="users.from" :to="users.to" :total="users.total" entityName="users" />
                            </div>
                            <Pagination :links="users.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
