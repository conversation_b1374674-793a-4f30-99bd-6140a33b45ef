<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { formatEnumOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();

interface Selection {
    id: number;
    uuid: string;
    category: string;
    value: string;
    description: string | null;
    sort_order: number;
    status: number;
    created_at: string;
    updated_at: string;
    created_by: {
        id: number;
        name: string;
    } | null;
    updated_by: {
        id: number;
        name: string;
    } | null;
}

interface Props {
    selections: PaginatedData<Selection>;
    categories: string[];
    statuses: Record<number, string>;
    filters: FilterOptions & {
        search?: string;
        category?: string;
        status?: number;
    };
}

const props = defineProps<Props>();

const form = useForm({});

const search = ref(props.filters.search || '');
const categoryName = ref(props.filters.category || '');
const selectionStatus = ref(props.filters.status || '');

const debouncedSearch = useDebounceFn(() => {
    form.get(
        route('selections.index', {
            search: search.value,
            category: categoryName.value,
            status: selectionStatus.value,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
}, 300);

const handleSearch = () => {
    debouncedSearch();
};

const handleReset = () => {
    search.value = '';
    categoryName.value = '';
    selectionStatus.value = '';
    form.get(route('selections.index'));
};

const handleView = (select: Selection) => {
    form.get(route('selections.show', select.id));
};

const handleEdit = (select: Selection) => {
    form.get(route('selections.edit', select.id));
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleDelete = (select: Selection) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Are you sure you want to delete this selection? This action cannot be undone.`,
        },
        submitOptions: {
            method: 'delete',
            url: route('selections.destroy', select.id),
            transform: (data) => ({
                ...data,
            }),
            successMessage: `Selection deleted successfully.`,
            errorMessage: `Failed to delete selection. Please try again.`,
            entityName: 'selection',
        },
    });
};

const handleToggleStatus = (data: { row: Selection; newStatus: number }) => {
    const { row: selection, newStatus } = data;
    updateStatus(selection, newStatus);
};
const updateStatus = (selection: Selection, newStatus: number) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Do you want to change the status of value is ${selection.value} and category is ${selection.category} to ${newStatus === 0 ? 'Active' : 'Inactive'}?`,
        },
        submitOptions: {
            method: 'put',
            url: route('selections.update-status', selection.id),
            transform: (data) => ({
                ...data,
                status: newStatus,
            }),
            successMessage: `Status of ${selection.value} has been updated.`,
            errorMessage: `Unable to update status for value is ${selection.value} and category is ${selection.category}. Please try again.`,
            entityName: 'selection',
        },
    });
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';
    form.get(
        route('selections.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const columns = [
    { field: 'category', label: 'Category', sortable: true, width: 'w-40' },
    { field: 'value', label: 'Value', sortable: true },
    { field: 'description', label: 'Description', sortable: true },
    { field: 'sort_order', label: 'Sort Order', sortable: true, width: 'w-15' },
    { field: 'updated_at', label: 'Updated At', sortable: true, width: 'w-40', format: (value) => formatDateTime(value) },
    { field: 'updated_by.name', label: 'Updated By', sortable: true },
    { field: 'status', label: 'Status', sortable: true, width: 'w-30' },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Selections" />

        <div class="px-4 py-3">
            <Heading title="Selections" pageNumber="P000049" />

            <SearchCard v-model:searchValue="search" searchLabel="Category" searchPlaceholder="Category" @search="handleSearch" @reset="handleReset">
                <template #additional-filters>
                    <div>
                        <Label class="pb-2" for="search-input">Value</Label>
                        <Input placeholder="Value" v-model="categoryName" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="status">Status</Label>
                        <Select :model-value="selectionStatus" @update:model-value="selectionStatus = Number($event)">
                            <SelectTrigger class="w-full">
                                <SelectValue placeholder="Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="option in formatEnumOptions(props.statuses)" :key="option.value" :value="option.value">
                                    {{ option.label }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </template>
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('selections.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add Selection
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="selections.data"
                        :sort-state="sortState"
                        empty-message="No selection found."
                        @sort="handleSort"
                        @edit="handleEdit"
                        @view="handleView"
                        @delete="handleDelete"
                        @toggleStatus="handleToggleStatus"
                        :showDeleteButton="true"
                        :showStatusToggle="true"
                        :showViewButton="true"
                    >
                        <!-- Custom cell renderers -->
                        <template #cell-status="{ row }">
                            <Badge
                                :class="[
                                    {
                                        'bg-green': row.status === 0,
                                        'bg-canary': row.status === 1,
                                    },
                                    'text-md w-15 px-1 py-0',
                                ]"
                            >
                                {{ row.status === 0 ? 'Active' : 'Inactive' }}
                            </Badge>
                        </template>
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="selections.from" :to="selections.to" :total="selections.total" entityName="selections" />
                            </div>
                            <Pagination :links="selections.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
