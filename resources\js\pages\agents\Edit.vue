<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useAuth } from '@/composables/useAuth';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const { formatEnumOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();
const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess } = useAuth();

interface Props {
    agent: {
        id: number;
        code: string;
        display_name: string;
        email: string;
        status: number;
        company: string | null;
        headquarter: string | null;
    };
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    agent_name: props.agent.display_name,
    company_name: props.agent.company,
    headquarter_name: props.agent.headquarter,
    email: props.agent.email,
    status: Number(props.agent.status),
});

const formFields = computed(() => {
    const fields = [];

    fields.push(
        {
            id: 'agent_name',
            label: 'Agent Name',
            type: 'show',
            modelValue: form.agent_name,
            updateValue: (value: string) => (form.agent_name = value),
        },
        {
            id: 'email',
            label: 'Email',
            type: 'input',
            required: false,
            placeholder: 'Email',
            error: form.errors.email,
            modelValue: form.email,
            updateValue: (value: string) => (form.email = value),
        },
    );

    if (!hasHeadquarterAccess.value) {
        fields.push({
            id: 'headquarter_name',
            label: 'Headquarter Name',
            type: 'show',
            modelValue: form.headquarter_name,
            updateValue: (value: string) => (form.headquarter_name = value),
        });
    }

    if (!hasCompanyAccess.value && !isHeadquarter.value) {
        fields.push({
            id: 'company_name',
            label: 'Company Name',
            type: 'show',
            modelValue: form.company_name,
            updateValue: (value: string) => (form.company_name = value),
        });
    }

    fields.push({
        id: 'status',
        label: 'Status',
        type: 'select',
        required: true,
        placeholder: 'Status',
        error: form.errors.status,
        options: formatEnumOptions(props.statuses),
        modelValue: form.status,
        updateValue: (value: number) => (form.status = value),
    });

    return fields;
});

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('agents.update', props.agent.id),
            entityName: 'agent',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Edit Agent" />
        <div class="px-4 py-3">
            <Heading title="Agent" pageNumber="P000024" description="Edit the selected agent record" />

            <AppCard title="Edit Agent" :form="form" backRoute="agents.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                                :class="field.class"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
