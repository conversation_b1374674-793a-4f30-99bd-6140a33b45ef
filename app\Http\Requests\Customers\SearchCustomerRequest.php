<?php

namespace App\Http\Requests\Customers;

use App\Http\Requests\BaseRequest;

class SearchCustomerRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'team' => ['required', 'exists:teams,id'],
            'customer_type' => ['required', 'exists:selections,id'],
            'name' => ['required', 'string', 'max:255'],
            'identity_no' => ['required', 'string', 'max:20'],
        ];

        return $rules;
    }
}
