<?php

namespace Database\Seeders\Company;

use App\Models\Headquarter;
use Illuminate\Database\Seeder;

class HeadquarterSeeder extends Seeder
{
    public function run(): void
    {
        $headquarters = [
            ['name' => 'HEADQUARTER001', 'code' => 'HQ001', 'display_name' => 'Headquarter001'],
            ['name' => 'HEADQUARTER002', 'code' => 'HQ002', 'display_name' => 'Headquarter002'],
            ['name' => 'HEADQUARTER003', 'code' => 'HQ003', 'display_name' => 'Headquarter003'],
            ['name' => 'HEADQUARTER004', 'code' => 'HQ004', 'display_name' => 'Headquarter004'],
            ['name' => 'HEADQUARTER005', 'code' => 'HQ005', 'display_name' => 'Headquarter005'],
        ];

        foreach ($headquarters as $hq) {
            $headquarter = Headquarter::firstOrCreate(
                ['name' => $hq['name']],
                [
                    'code' => $hq['code'],
                    'display_name' => $hq['display_name'],
                ]
            );

            $headquarter->companies()->firstOrCreate(
                ['name' => $hq['name']],
                [
                    'code' => $hq['code'],
                    'display_name' => $hq['display_name'],
                    'is_headquarter' => true,
                ]
            );
        }

        $this->command->info('Headquarter created successfully');
    }
}
