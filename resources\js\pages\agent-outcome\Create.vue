<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useAuth } from '@/composables/useAuth';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed, onMounted, watch } from 'vue';

const { formatSelectionOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();
const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess, primaryHeadquarterId, primaryCompanyId } = useAuth();

interface Company {
    id: number;
    display_name: string;
    code: string;
    headquarter_id: number | null;
    is_headquarter: boolean;
}

interface OutcomeType {
    id: number;
    name: string;
    companies: Company[];
}

interface Agent {
    id: number;
    name: string;
    display_name: string;
    code: string;
    companies: Company[];
}

interface Props {
    headquarters: Company[];
    companies: Company[];
    agents: Agent[];
    outcomeTypes: OutcomeType[];
}

const props = defineProps<Props>();

const form = useForm({
    agent_id: null as number | null,
    outcome_types_id: null as number | null,
    amount: '',
    remark: '',
    headquarter_id: hasHeadquarterAccess.value ? primaryHeadquarterId.value : null,
    company_id: hasCompanyAccess.value ? primaryCompanyId.value : null,
});

onMounted(() => {
    if (form.headquarter_id && !form.company_id) {
        if (isHeadquarter.value && form.headquarter_id) {
            const headquarterCompany = props.companies.find((company) => company.headquarter_id === form.headquarter_id && company.is_headquarter);

            if (headquarterCompany) {
                form.company_id = headquarterCompany.id;
            }
            return;
        }

        const availableCompanies = props.companies.filter((company) => company.headquarter_id === form.headquarter_id);
        if (availableCompanies.length === 1 && !hasCompanyAccess.value) {
            form.company_id = availableCompanies[0].id;
        }

        if (primaryCompanyId.value) {
            const primaryCompany = availableCompanies.find((company) => company.id === primaryCompanyId.value);
            if (primaryCompany) {
                form.company_id = primaryCompany.id;
            }
        }
    }
});

const formFields = computed(() => {
    const fields = [];

    if (!hasHeadquarterAccess.value) {
        fields.push({
            id: 'headquarter_id',
            label: 'Headquarter Name',
            type: 'select',
            required: true,
            placeholder: 'Headquarter Name',
            error: form.errors.headquarter_id,
            options: formatSelectionOptions(props.headquarters, 'id', 'display_name'),
            modelValue: form.headquarter_id,
            updateValue: (value: number) => (form.headquarter_id = value),
        });
    }

    if (!hasCompanyAccess.value && !isHeadquarter.value) {
        fields.push({
            id: 'company_id',
            label: 'Company Name',
            type: 'select',
            required: true,
            placeholder: 'Company Name',
            error: form.errors.company_id,
            options: formatSelectionOptions(filteredCompanies.value, 'id', 'display_name'),
            modelValue: form.company_id,
            updateValue: (value: number) => (form.company_id = value),
        });
    }

    fields.push(
        {
            id: 'agent_id',
            label: 'Agent Name',
            type: 'select',
            required: true,
            placeholder: 'Agent Name',
            error: form.errors.agent_id,
            options: formatSelectionOptions(filteredAgents.value, 'id', 'display_name'),
            modelValue: form.agent_id,
            updateValue: (value: number) => (form.agent_id = value),
        },
        {
            id: 'outcome_types_id',
            label: 'Outcome Type',
            type: 'select',
            required: true,
            placeholder: 'Outcome Type',
            error: form.errors.outcome_types_id,
            options: formatSelectionOptions(filteredOutcomeTypes.value, 'id', 'name'),
            modelValue: form.outcome_types_id,
            updateValue: (value: number) => (form.outcome_types_id = value),
        },
        {
            id: 'amount',
            label: 'Amount',
            type: 'input',
            required: true,
            placeholder: 'Amount',
            error: form.errors.amount,
            modelValue: form.amount,
            updateValue: (value: string) => (form.amount = value),
        },
        {
            id: 'remark',
            label: 'Remark',
            type: 'textarea',
            required: false,
            placeholder: 'Remark',
            error: form.errors.remark,
            modelValue: form.remark,
            class: 'col-span-full w-full rounded',
            updateValue: (value: string) => (form.remark = value),
        },
    );

    return fields;
});

const filteredCompanies = computed(() => {
    if (!form.headquarter_id) return [];
    return props.companies.filter((company) => company.headquarter_id === form.headquarter_id);
});

const filteredAgents = computed(() => {
    if (!form.company_id) return [];
    return props.agents.filter((agent) => agent.companies?.some((company) => company.id === form.company_id));
});

const filteredOutcomeTypes = computed(() => {
    if (!form.company_id) return [];
    return props.outcomeTypes.filter((types) => types.companies?.some((company) => company.id === form.company_id));
});

watch(
    () => form.headquarter_id,
    (newValue) => {
        if (newValue) {
            form.company_id = null;
            form.agent_id = null;
            form.outcome_types_id = null;
        }
    },
);

watch(
    () => form.company_id,
    (newCompanyId) => {
        if (newCompanyId) {
            form.outcome_types_id = null;
            form.agent_id = null;
        }
    },
);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('agent-outcomes.store'),
            entityName: 'agent outcome',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Create Agent Outcome" />
        <div class="px-4 py-3">
            <Heading title="Agent Outcome" pageNumber="P000030" description="Create a new agent outcome record" />

            <AppCard title="Add New Agent Outcome" :form="form" backRoute="agent-outcomes.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                                :class="field.class"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
