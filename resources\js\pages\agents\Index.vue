<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import FormSelect from '@/components/form/FormSelect.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/composables/useAuth';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { formatEnumOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();
const { hasHeadquarterAccess, hasCompanyAccess } = useAuth();

interface Agent {
    id: number;
    uuid: string;
    code: string;
    name: string;
    email: string;
    remark: string;
    status: number;
    headquarter: {
        id: number;
        display_name: string;
        code: string;
    } | null;
    company: {
        id: number;
        display_name: string;
        code: string;
    } | null;
    created_at: string;
    updated_at: string;
    created_by: {
        id: number;
        name: string;
    } | null;
    updated_by: {
        id: number;
        name: string;
    } | null;
}

interface Props {
    agents: PaginatedData<Agent>;
    filters: FilterOptions & {
        name?: string;
        status?: number;
    };
    statuses: Record<number, string>;
}

const props = defineProps<Props>();
const form = useForm({});

const searchValue = ref(props.filters.name || '');
const status = ref(props.filters.status || '');

const debouncedSearch = useDebounceFn(() => {
    form.get(
        route('agents.index', {
            name: searchValue.value,
            status: status.value,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
}, 300);

const handleSearch = () => {
    debouncedSearch();
};

const handleReset = () => {
    searchValue.value = '';
    status.value = '';
    form.get(route('agents.index'));
};

const handleView = (agent: Agent) => {
    form.get(route('agents.show', agent.id));
};

const handleEdit = (agent: Agent) => {
    form.get(route('agents.edit', agent.id));
};

const handleToggleStatus = (data: { row: Agent; newStatus: number }) => {
    const { row: agent, newStatus } = data;
    updateStatus(agent, newStatus);
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';
    form.get(
        route('agents.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const updateStatus = (agent: Agent, newStatus: number) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Do you want to change the status of ${agent.name} to ${newStatus === 0 ? 'Active' : 'Inactive'}?`,
        },
        submitOptions: {
            method: 'put',
            url: route('agents.update-status', agent.id),
            transform: (data) => ({
                ...data,
                status: newStatus,
            }),
            successMessage: `Status of ${agent.name} has been updated.`,
            errorMessage: `Unable to update status for ${agent.name}. Please try again.`,
            entityName: 'agent',
        },
    });
};

const columns = computed(() => {
    const cols = [
        { field: 'code', label: 'Code', sortable: true, width: 'w-40' },
        { field: 'display_name', label: 'Agent Name', sortable: true },
    ];

    if (!hasHeadquarterAccess.value) {
        cols.push({ field: 'headquarter.display_name', label: 'Headquarter Name', sortable: true });
    }

    if (!hasCompanyAccess.value) {
        cols.push({ field: 'company.display_name', label: 'Company Name', sortable: true });
    }

    return [
        ...cols,
        { field: 'email', label: 'Email', sortable: true },
        { field: 'updated_at', label: 'Updated At', sortable: true, width: 'w-40', format: (value) => formatDateTime(value) },
        { field: 'updated_by.name', label: 'Updated By', sortable: true },
        { field: 'status', label: 'Status', sortable: true, width: 'w-30' },
        { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
    ];
});

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Agents" />

        <div class="px-4 py-3">
            <Heading title="Agent" pageNumber="P000026" />

            <SearchCard
                v-model:searchValue="searchValue"
                searchLabel="Agent Name"
                searchPlaceholder="Agent Name"
                @search="handleSearch"
                @reset="handleReset"
            >
                <template #additional-filters>
                    <div>
                        <!-- Status -->
                        <FormSelect
                            id="status"
                            label="Status"
                            :model-value="status"
                            @update:model-value="status = Number($event)"
                            type="select"
                            :options="formatEnumOptions(props.statuses)"
                            placeholder="Status"
                            :error="form.errors.status"
                            labelClass="pb-1"
                        />
                    </div>
                </template>
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('agents.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Agent
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="agents.data"
                        :sort-state="sortState"
                        empty-message="No agent found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        @toggleStatus="handleToggleStatus"
                        :showDeleteButton="false"
                    >
                        <!-- Custom cell renderers -->
                        <template #cell-status="{ row }">
                            <Badge
                                :class="[
                                    {
                                        'bg-green': row.status === 0,
                                        'bg-canary': row.status === 1,
                                    },
                                    'text-md w-15 px-1 py-0',
                                ]"
                            >
                                {{ row.status === 0 ? 'Active' : 'Inactive' }}
                            </Badge>
                        </template>
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="agents.from" :to="agents.to" :total="agents.total" entityName="agent" />
                            </div>
                            <Pagination :links="agents.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
