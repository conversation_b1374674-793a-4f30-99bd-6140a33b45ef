<?php

namespace App\Http\Requests\Teams;

use App\Http\Requests\BaseRequest;
use App\Models\Company;

class StoreTeamRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'code' => ['required', 'string', 'max:32'],
            'name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'string'],
            'website' => ['nullable', 'string'],
            'selection_telephone_country_id' => ['nullable', 'exists:selections,id'],
            'telephone' => ['nullable', 'string', 'max:24'],
            'status' => ['required', 'integer'],
            'remark' => ['nullable', 'string'],
            'headquarter_id' => ['required', 'exists:companies,id'],
            'company_id' => ['required', 'exists:companies,id'],
            'line_1' => ['nullable', 'string', 'max:255'],
            'line_2' => ['nullable', 'string', 'max:255'],
            'postcode' => ['nullable', 'string', 'max:10'],
            'city' => ['nullable', 'string', 'max:255'],
            'selection_state_id' => ['nullable', 'exists:selections,id'],
            'selection_country_id' => ['nullable', 'exists:selections,id'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'code.required' => 'The team code is required.',
            'code.max' => 'The team code cannot exceed 2 characters.',
            'name.required' => 'The team name is required.',
            'name.max' => 'The team name cannot exceed 255 characters.',
            'headquarter_id.required' => 'Please select a headquarter.',
            'headquarter_id.exists' => 'The selected headquarter does not exist.',
            'company_id.required' => 'Please select a company.',
            'company_id.exists' => 'The selected company does not exist.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate relationship between headquarter and company
            if ($this->filled('company_id') && $this->filled('headquarter_id')) {
                $company = Company::find($this->company_id);
                if (! $company || $company->headquarter_id != $this->headquarter_id) {
                    $validator->errors()->add(
                        'company_id',
                        'The selected company does not belong to the selected headquarter.'
                    );
                }
            }
        });
    }
}
