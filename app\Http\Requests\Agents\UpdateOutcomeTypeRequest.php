<?php

namespace App\Http\Requests\Agents;

use App\Http\Requests\BaseRequest;

class UpdateOutcomeTypeRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'teams' => ['required', 'array', 'min:1'],
            'teams.*' => ['exists:teams,id'],
        ];
    }
}
