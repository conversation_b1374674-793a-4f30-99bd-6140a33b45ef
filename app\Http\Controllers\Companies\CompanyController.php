<?php

namespace App\Http\Controllers\Companies;

use App\Enums\Company\CompanyStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Companies\StoreCompanyRequest;
use App\Http\Requests\Companies\UpdateCompanyRequest;
use App\Http\Requests\Companies\UpdateCompanyStatusRequest;
use App\Http\Resources\Companies\CompanyResource;
use App\Models\Company;
use App\Models\Headquarter;
use App\Traits\HandlesFileStorage;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class CompanyController extends Controller
{
    use HandlesFileStorage, QueryFilterableTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', Company::class);

        $query = Company::forUser()
            ->companyOnly()
            ->withAuditUsers()
            ->with(['headquarter:id,code,display_name']);

        $this->applySearchFilter($query, $request);
        $this->applyStatusFilter($query, $request);
        $this->applySorting($query, $request);

        $companies = $this->applyPagination($query, $request, 10,
            fn ($company) => (new CompanyResource($company))->toArray($request));

        return Inertia::render('companies/Index', [
            'companies' => $companies,
            'statuses' => CompanyStatus::options(),
            'filters' => $request->only(['name', 'status', 'per_page', 'sort_field', 'sort_direction']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $this->authorize('create', Company::class);

        return Inertia::render('companies/Create', [
            'headquarters' => Headquarter::getForDropdown(),
            'statuses' => CompanyStatus::options(),
            'defaultStatus' => CompanyStatus::ACTIVE->value,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCompanyRequest $request): RedirectResponse
    {
        $this->authorize('create', Company::class);

        try {
            DB::beginTransaction();

            $company = Company::create($request->validated());

            $filePath = $this->storeUploadedFile($request->logo, 'uploads/'.implode('/', [
                'company',
                $company->uuid,
            ]));

            $company->update([
                'logo' => $filePath,
            ]);

            DB::commit();

            return Redirect::route('companies.index')->with('success', 'Company created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()->withInput()->with('error', 'Failed to create company. '.$e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Company $company): Response
    {
        $this->authorize('view', $company);

        $company->withAuditUsers();
        $company->load(['headquarter:id,code,display_name']);

        return Inertia::render('companies/Show', [
            'company' => (new CompanyResource($company))->toArray(request(), true),
            'logo' => $company->getLogoUrl(),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Company $company): Response
    {
        $this->authorize('update', $company);

        return Inertia::render('companies/Edit', [
            'company' => $company,
            'headquarters' => Headquarter::getForDropdown(),
            'statuses' => CompanyStatus::options(),
            'logo' => $company->getLogoUrl(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCompanyRequest $request, Company $company): RedirectResponse
    {
        $this->authorize('update', $company);

        try {
            DB::beginTransaction();

            $company->update(collect($request->validated())->except('logo')->all());

            $storedPath = $this->storeBase64File($request->input('logo'), 'uploads/company/'.$company->uuid, $company->logo);

            $company->update([
                'logo' => $storedPath,
            ]);

            DB::commit();

            return Redirect::route('companies.index')->with('success', 'Company updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()->withInput()->with('error', 'Failed to update company. '.$e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Company $company): RedirectResponse
    {
        $this->authorize('delete', $company);

        try {
            $company->delete();

            return Redirect::route('companies.index')->with('success', 'Company deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to delete company. '.$e->getMessage());
        }
    }

    /**
     * Update the status of the specified resource.
     */
    public function updateStatus(UpdateCompanyStatusRequest $request, Company $company): RedirectResponse
    {
        $this->authorize('update', $company);

        try {
            $company->update(['status' => $request->validated('status')]);

            return Redirect::back()->with('success', 'Company status updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update company status. '.$e->getMessage());
        }
    }
}
