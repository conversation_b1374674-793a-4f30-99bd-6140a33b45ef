<?php

use App\Enums\Collateral\CollateralStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('collaterals', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Basic information
            $table->foreignId('selection_customer_type_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('customer_type', 36)->nullable();
            $table->foreignId('selection_type_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('type', 36)->nullable();
            $table->string('name')->nullable();
            $table->string('identity_no', 20)->nullable();
            $table->string('company_name')->nullable();
            $table->string('business_registration_no', 100)->nullable();

            // Status
            $table->unsignedTinyInteger('status')->default(CollateralStatus::ACTIVE->value);

            // Additional information
            $table->text('remark')->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('collaterals');
    }
};
