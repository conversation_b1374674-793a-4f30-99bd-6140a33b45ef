<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * CollateralValuer model for managing valuation details of collaterals
 */
class CollateralValuer extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'collateral_id',
        'valuation_amount',
        'valuer',
        'valuation_received_date',
        'land_search_received_date',
        'is_primary',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'uuid' => 'string',
        'collateral_id' => 'integer',
        'valuation_amount' => 'decimal:2',
        'valuation_received_date' => 'datetime',
        'land_search_received_date' => 'datetime',
        'is_primary' => 'boolean',
        'deleted_at' => 'datetime',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
    ];

    /**
     * Get the collateral that owns this valuer.
     */
    public function collateral(): BelongsTo
    {
        return $this->belongsTo(Collateral::class);
    }
}
