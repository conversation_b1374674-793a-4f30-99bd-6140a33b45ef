<?php

namespace App\Http\Requests\Companies;

use App\Http\Requests\BaseRequest;
use App\Models\Headquarter;
use Illuminate\Validation\Rule;

class StoreCompanyRequest extends BaseRequest
{
    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'code' => strtoupper(str_replace(' ', '', $this->code)),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'headquarter_id' => [
                'required',
                'exists:companies,id',
                function ($attribute, $value, $fail) {
                    $headquarter = Headquarter::find($value);
                    if (! $headquarter) {
                        $fail('The selected parent must be a headquarter.');
                    }
                },
            ],
            'display_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('companies', 'name'),
            ],
            'code' => [
                'required',
                'string',
                'max:32',
                Rule::unique('companies', 'code'),
            ],
            'business_registration_no' => ['nullable', 'string', 'max:100'],
            'old_business_registration_no' => ['nullable', 'string', 'max:100'],
            'status' => ['required', 'integer'],
            'logo' => ['nullable', 'file'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'headquarter_id.required' => 'Please select a headquarter for this company.',
            'headquarter_id.exists' => 'The selected headquarter does not exist.',
            'display_name.required' => 'The company name is required.',
            'display_name.max' => 'The company name cannot exceed 255 characters.',
            'display_name.unique' => 'This company name is already in use.',
            'code.required' => 'The company code is required.',
            'code.unique' => 'This company code is already in use.',
            'code.max' => 'The company code cannot exceed 32 characters.',
            'status.required' => 'Please select a status for this company.',
        ];
    }
}
