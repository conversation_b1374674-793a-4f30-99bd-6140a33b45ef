<?php

namespace App\Traits;

trait UniqueCodeTrait
{
    /**
     * Generate a unique code with the given prefix
     */
    public static function generateUniqueCodeWithPrefix(string $prefix, int $padLength = 3): string
    {
        $lastCode = self::select('code')
            ->where('code', 'like', $prefix.'%')
            ->withTrashed()
            ->orderBy('code', 'desc')
            ->value('code');

        $nextNumber = $lastCode
            ? (int) substr($lastCode, strlen($prefix)) + 1
            : 1;

        return $prefix.str_pad($nextNumber, $padLength, '0', STR_PAD_LEFT);
    }
}
