<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useAuth } from '@/composables/useAuth';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed, onMounted, watch } from 'vue';

const { formatEnumOptions, formatSelectionOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();
const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess, primaryHeadquarterId, primaryCompanyId } = useAuth();

interface Company {
    id: number;
    display_name: string;
    code: string;
    headquarter_id: number | null;
    is_headquarter: boolean;
}

interface Props {
    headquarters: Company[];
    companies: Company[];
    defaultStatus: number;
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    code: '',
    display_name: '',
    email: '',
    status: props.defaultStatus,
    headquarter_id: hasHeadquarterAccess.value ? primaryHeadquarterId.value : null,
    company_id: hasCompanyAccess.value ? primaryCompanyId.value : null,
});

onMounted(() => {
    if (form.headquarter_id && !form.company_id) {
        if (isHeadquarter.value && form.headquarter_id) {
            const headquarterCompany = props.companies.find((company) => company.headquarter_id === form.headquarter_id && company.is_headquarter);

            if (headquarterCompany) {
                form.company_id = headquarterCompany.id;
            }
            return;
        }

        const availableCompanies = props.companies.filter((company) => company.headquarter_id === form.headquarter_id);
        if (availableCompanies.length === 1 && !hasCompanyAccess.value) {
            form.company_id = availableCompanies[0].id;
        }

        if (primaryCompanyId.value) {
            const primaryCompany = availableCompanies.find((company) => company.id === primaryCompanyId.value);
            if (primaryCompany) {
                form.company_id = primaryCompany.id;
            }
        }
    }
});

const formFields = computed(() => {
    const fields = [];

    fields.push(
        {
            id: 'display_name',
            label: 'Agent Name',
            type: 'input',
            required: true,
            placeholder: 'Agent Name',
            error: form.errors.display_name,
            modelValue: form.display_name,
            updateValue: (value: string) => (form.display_name = value),
        },
        {
            id: 'email',
            label: 'Email',
            type: 'input',
            required: false,
            placeholder: 'Email',
            error: form.errors.email,
            modelValue: form.email,
            updateValue: (value: string) => (form.email = value),
        },
    );

    if (!hasHeadquarterAccess.value) {
        fields.push({
            id: 'headquarter_id',
            label: 'Headquarter Name',
            type: 'select',
            required: true,
            placeholder: 'Headquarter Name',
            error: form.errors.headquarter_id,
            options: formatSelectionOptions(props.headquarters, 'id', 'display_name'),
            modelValue: form.headquarter_id,
            updateValue: (value: number) => (form.headquarter_id = value),
        });
    }

    if (!hasCompanyAccess.value && !isHeadquarter.value) {
        fields.push({
            id: 'company_id',
            label: 'Company Name',
            type: 'select',
            required: true,
            placeholder: 'Company Name',
            error: form.errors.company_id,
            options: formatSelectionOptions(filteredCompanies.value, 'id', 'display_name'),
            modelValue: form.company_id,
            updateValue: (value: number) => (form.company_id = value),
        });
    }

    fields.push({
        id: 'status',
        label: 'Status',
        type: 'select',
        required: true,
        placeholder: 'Status',
        error: form.errors.status,
        options: formatEnumOptions(props.statuses),
        modelValue: form.status,
        updateValue: (value: number) => (form.status = value),
    });

    return fields;
});

const filteredCompanies = computed(() => {
    if (!form.headquarter_id) return [];
    return props.companies.filter((company) => company.headquarter_id === form.headquarter_id);
});

watch(
    () => form.headquarter_id,
    () => {
        form.company_id = null;

        if (isHeadquarter.value && form.headquarter_id) {
            const headquarterCompany = props.companies.find((company) => company.headquarter_id === form.headquarter_id && company.is_headquarter);

            if (headquarterCompany) {
                form.company_id = headquarterCompany.id;
            }
        }
    },
);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('agents.store'),
            entityName: 'agent',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Create Agent" />
        <div class="px-4 py-3">
            <Heading title="Agent" pageNumber="P000024" description="Create a new agent record" />

            <AppCard title="Add New Agent" :form="form" backRoute="agents.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                                :class="field.class"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
