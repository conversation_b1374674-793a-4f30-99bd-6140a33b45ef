<?php

namespace App\Http\Requests\Customers;

use App\Http\Requests\BaseRequest;

class StoreCustomerRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'selection_type_id' => ['required', 'exists:selections,id'],
            'team' => ['required', 'exists:teams,id'],
            'name' => ['required', 'string', 'max:255'],
            'identity_no' => ['required', 'string', 'max:20'],
            'email' => ['nullable', 'string', 'email', 'max:255'],
            'remark' => ['nullable', 'string', 'max:1000'],

            'contacts.*.selection_type_id' => ['required', 'exists:selections,id'],
            'contacts.*selection_telephone_country_id' => ['required', 'exists:selections,id'],
            'contacts.*selection_mobile_country_id' => ['required', 'exists:selections,id'],
            'contacts.*.telephone' => ['required_without:contacts.*.mobile_phone', 'nullable', 'string', 'max:20'],
            'contacts.*.mobile_phone' => ['required_without:contacts.*.telephone', 'nullable', 'string', 'max:20'],
            'contacts.*.can_receive_sms' => ['required', 'boolean'],
            'contacts.*.is_primary' => ['required', 'boolean'],

            'addresses.*.selection_type_id' => ['required', 'exists:selections,id'],
            'addresses.*.line_1' => ['required', 'string', 'max:255'],
            'addresses.*.line_2' => ['nullable', 'string', 'max:255'],
            'addresses.*.postcode' => ['required', 'string', 'max:10'],
            'addresses.*.city' => ['required', 'string', 'max:50'],
            'addresses.*.selection_state_id' => ['required', 'exists:selections,id'],
            'addresses.*.state' => ['nullable', 'string', 'max:50'],
            'addresses.*.selection_country_id' => ['required', 'exists:selections,id'],
            'addresses.*.country' => ['nullable', 'string', 'max:50'],
            'addresses.*.is_primary' => ['required', 'boolean'],

            'collateral.*.selection_customer_type_id' => ['required', 'exists:selections,id'],
            'collateral.*.selection_type_id' => ['required', 'exists:selections,id'],
            'collateral.*.name' => ['required', 'string', 'max:255'],
            'collateral.*.identity_no' => ['required', 'string', 'max:20'],

            'collateral.*.valuers.*.valuer' => ['required', 'string', 'max:255'],
            'collateral.*.valuers.*.valuation_amount' => ['required', 'numeric', 'min:0'],
            'collateral.*.valuers.*.valuation_received_date' => ['required', 'date'],
            'collateral.*.valuers.*.land_search_received_date' => ['nullable', 'date'],
            'collateral.*.valuers.*.is_primary' => ['required', 'boolean'],

            'collateral.*.property_owners.*.name' => ['required', 'string', 'max:255'],
            'collateral.*.property_owners.*.identity_no' => ['required', 'string', 'max:20'],
            'collateral.*.property_owners.*.telephone' => ['nullable', 'string', 'max:20'],
            'collateral.*.property_owners.*.mobile_phone' => ['nullable', 'string', 'max:20'],
            'collateral.*.property_owners.*.address.line_1' => ['nullable', 'string', 'max:255'],
            'collateral.*.property_owners.*.address.line_2' => ['nullable', 'string', 'max:255'],
            'collateral.*.property_owners.*.address.postcode' => ['nullable', 'string', 'max:10'],
            'collateral.*.property_owners.*.address.city' => ['nullable', 'string', 'max:50'],
            'collateral.*.property_owners.*.address.selection_state_id' => ['nullable', 'exists:selections,id'],
            'collateral.*.property_owners.*.address.state' => ['nullable', 'string', 'max:50'],
            'collateral.*.property_owners.*.address.selection_country_id' => ['nullable', 'exists:selections,id'],
            'collateral.*.property_owners.*.address.country' => ['nullable', 'string', 'max:50'],

            'document.*' => ['nullable', 'array'],

        ];

        if ((int) $this->input('selection_type_id') === 29) {
            $rules += [
                'old_identity_no' => ['nullable', 'string', 'max:20'],
                'registration_date' => ['required', 'date'],
                'years_of_incorporation' => ['required', 'integer', 'min:0'],

                'company.selection_nature_of_business_id' => ['nullable', 'exists:selections,id'],
                'company.selection_country_of_business_id' => ['nullable', 'exists:selections,id'],
                'company.current_paid_up_capital' => ['nullable', 'string', 'max:255'],
                'company.business_turnover' => ['nullable', 'string', 'max:255'],
                'company.business_turnover_date' => ['nullable', 'date'],
                'company.business_net_income' => ['nullable', 'string', 'max:255'],
                'company.business_net_income_date' => ['nullable', 'date'],

                'owners.*.selection_type_id' => ['required', 'exists:selections,id'],
                'owners.*.name' => ['required', 'string', 'max:255'],
                'owners.*.identity_no' => ['required', 'string', 'max:20'],
                'owners.*.selection_nationality_id' => ['required', 'exists:selections,id'],
                'owners.*.share_unit' => ['required', 'numeric', 'min:0'],

                'collateral.*.selection_customer_type_id' => ['required', 'exists:selections,id'],
                'collateral.*.company_name' => ['required', 'string'],
                'collateral.*.business_registration_no' => ['required', 'string', 'max:100'],
            ];
        } else {
            $rules += [
                'birth_date' => ['nullable', 'date'],
                'age' => ['nullable', 'integer', 'min:0'],
                'selection_race_id' => ['required', 'exists:selections,id'],
                'race' => ['nullable', 'string', 'max:50'],
                'selection_gender_id' => ['required', 'exists:selections,id'],
                'gender' => ['nullable', 'string', 'max:50'],
                'selection_marriage_status_id' => ['required', 'exists:selections,id'],
                'marriage_status' => ['nullable', 'string', 'max:50'],
                'selection_nationality_id' => ['required', 'exists:selections,id'],
                'nationality' => ['nullable', 'string', 'max:50'],
                'selection_education_level_id' => ['required', 'exists:selections,id'],
                'education_level' => ['nullable', 'string', 'max:50'],

                'employment.selection_terms_of_employment_id' => ['required', 'exists:selections,id'],
                'employment.term_of_employment' => ['nullable', 'string', 'max:50'],
                'employment.length_service_year' => ['nullable', 'integer', 'min:0'],
                'employment.length_service_month' => ['nullable', 'integer', 'min:0'],
                'employment.job_position' => ['nullable', 'string', 'max:255'],
                'employment.selection_occupation_id' => ['nullable', 'exists:selections,id'],
                'employment.occupation' => ['nullable', 'string', 'max:50'],
                'employment.selection_business_category_id' => ['nullable', 'exists:selections,id'],
                'employment.business_category' => ['nullable', 'string', 'max:50'],
                'employment.gross_income' => ['nullable', 'numeric', 'min:0'],
                'employment.net_income' => ['nullable', 'numeric', 'min:0'],
                'employment.selection_telephone_country_id' => ['nullable', 'exists:selections,id'],
                'employment.selection_mobile_country_id' => ['nullable', 'exists:selections,id'],
                'employment.telephone' => ['nullable', 'string', 'max:20'],
                'employment.mobile_phone' => ['nullable', 'string', 'max:20'],
                'employment.employer_name' => ['nullable', 'string', 'max:255'],
                'employment.address.selection_type_id' => ['nullable', 'exists:selections,id'],
                'employment.address.line_1' => ['nullable', 'string', 'max:255'],
                'employment.address.line_2' => ['nullable', 'string', 'max:255'],
                'employment.address.postcode' => ['nullable', 'string', 'max:10'],
                'employment.address.city' => ['nullable', 'string', 'max:50'],
                'employment.address.selection_state_id' => ['nullable', 'exists:selections,id'],
                'employment.address.state' => ['nullable', 'string', 'max:50'],
                'employment.address.selection_country_id' => ['nullable', 'exists:selections,id'],
                'employment.address.country' => ['nullable', 'string', 'max:50'],
                'employment.address.is_primary' => ['nullable', 'string', 'max:50'],
            ];
        }

        foreach ($this->input('collateral', []) as $index => $collateral) {
            if (isset($collateral['selection_type_id']) && (int) $collateral['selection_type_id'] === 14) {
                $rules["collateral.$index.property.ownership_no"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.lot_number"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.selection_land_category_id"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.land_category"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.land_category_other"] = ['nullable', 'string', 'max:100'];
                $rules["collateral.$index.property.selection_type_of_property_id"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.type_of_property"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.land_size"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.selection_land_size_unit"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.land_size_unit"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.selection_land_status_id"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.land_status"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.city"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.location"] = ['required', 'string', 'max:255'];
                $rules["collateral.$index.property.district"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.selection_built_up_area_unit"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.built_up_area_unit"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.no_syit_piawai"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.certified_plan_no"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.built_up_area_of_property"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.address.line_1"] = ['required', 'string', 'max:255'];
                $rules["collateral.$index.property.address.line_2"] = ['nullable', 'string', 'max:255'];
                $rules["collateral.$index.property.address.postcode"] = ['required', 'string', 'max:10'];
                $rules["collateral.$index.property.address.city"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.address.selection_state_id"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.address.state"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.address.selection_country_id"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.address.country"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.address.is_primary"] = ['required', 'boolean'];
            } else {
                $rules["collateral.$index.remark"] = ['required', 'string', 'max:1000'];
            }
        }

        return $rules;
    }
}
