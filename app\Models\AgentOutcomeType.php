<?php

namespace App\Models;

use App\Enums\AccessControl\RoleName;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

/**
 * AgentOutcomeType model for managing agent outcome types
 */
class AgentOutcomeType extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'company_id',
        'name',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the companies associated with this agent outcome type.
     */
    public function companies(): MorphToMany
    {
        return $this->morphToMany(Company::class, 'companyable');
    }

    /**
     * Get the user's resolve hierarchy.
     */
    public function getResolveHierarchy(): array
    {
        $agentProfile = $this;

        if (! $this) {
            return ['headquarter' => null, 'company' => null];
        }

        if ($directCompany = $agentProfile->companies->first()) {
            $headquarter = ($directCompany->headquarter_id && $directCompany->parent)
                ? $directCompany->parent
                : null;

            return [
                'headquarter' => $headquarter,
                'company' => $directCompany,
            ];
        }

        return [
            'headquarter' => null,
            'company' => null,
        ];
    }

    /**
     * Scope a query to get agent outcome types for dropdown lists.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDropdown($query)
    {
        return $query->select('id', 'name', 'uuid')
            ->orderBy('name');
    }

    /**
     * Get agent outcome types for dropdown selection
     *
     * @param  array  $columns  Additional columns to select
     * @return array Formatted agent outcome type data
     */
    public static function getForDropdown(array $columns = []): array
    {
        $defaultColumns = ['id', 'name', 'uuid'];
        $selectColumns = array_merge($defaultColumns, $columns);

        return self::forDropdown()
            ->select($selectColumns)
            ->get()
            ->map(function ($outcomeType) {
                return [
                    'id' => $outcomeType->id,
                    'name' => $outcomeType->name,
                    'uuid' => $outcomeType->uuid,
                    'companies' => $outcomeType->companies->map(fn ($company) => [
                        'id' => $company->id,
                        'name' => $company->name,
                    ])->toArray(),
                ];
            })
            ->toArray();
    }

    /**
     * Get headquarters accessible to the specified user.
     *
     * @param  \App\Models\User|null  $user
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function forUser($user = null)
    {
        $user = $user ?? auth()->user();

        if ($user->hasRole(RoleName::SUPER_ADMIN)) {
            return self::query();
        }

        $userCompanyIds = $user->adminProfiles
            ->flatMap(function ($profile) {
                $companyIds = collect();

                if ($profile->headquarters()->exists()) {
                    $companyIds = $companyIds->merge(
                        Company::whereIn('headquarter_id', $profile->headquarters()->pluck('id'))->pluck('id')
                    );
                }

                $companyIds = $companyIds->merge($profile->companies()->pluck('id'));

                $teamCompanyIds = $profile->teams()
                    ->get()
                    ->flatMap(function ($team) {
                        $companyIds = $team->companies()->pluck('id');

                        $headquarterIds = $team->companies()
                            ->where('is_headquarter', true)
                            ->pluck('id');

                        if ($headquarterIds->isNotEmpty()) {
                            $companyIds = $companyIds->merge(
                                Company::whereIn('headquarter_id', $headquarterIds)->pluck('id')
                            );
                        }

                        return $companyIds;
                    });

                return $companyIds->merge($teamCompanyIds);
            })
            ->unique();

        return self::whereIn('company_id', $userCompanyIds);
    }
}
