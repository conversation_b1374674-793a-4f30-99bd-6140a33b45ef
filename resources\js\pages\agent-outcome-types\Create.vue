<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useAuth } from '@/composables/useAuth';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed, onMounted, watch } from 'vue';

const { formatSelectionOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();
const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess, primaryHeadquarterId, primaryCompanyId } = useAuth();

interface Company {
    id: number;
    display_name: string;
    code: string;
    headquarter_id: number | null;
    is_headquarter: boolean;
}

interface Props {
    headquarters: Company[];
    companies: Company[];
}

const props = defineProps<Props>();

const form = useForm({
    name: '',
    headquarter_id: hasHeadquarterAccess.value ? primaryHeadquarterId.value : null,
    company_id: hasCompanyAccess.value ? primaryCompanyId.value : null,
});

onMounted(() => {
    if (form.headquarter_id && !form.company_id) {
        if (isHeadquarter.value && form.headquarter_id) {
            const headquarterCompany = props.companies.find((company) => company.headquarter_id === form.headquarter_id && company.is_headquarter);

            if (headquarterCompany) {
                form.company_id = headquarterCompany.id;
            }
            return;
        }

        const availableCompanies = props.companies.filter((company) => company.headquarter_id === form.headquarter_id);
        if (availableCompanies.length === 1 && !hasCompanyAccess.value) {
            form.company_id = availableCompanies[0].id;
        }

        if (primaryCompanyId.value) {
            const primaryCompany = availableCompanies.find((company) => company.id === primaryCompanyId.value);
            if (primaryCompany) {
                form.company_id = primaryCompany.id;
            }
        }
    }
});

const formFields = computed(() => {
    const fields = [];

    fields.push({
        id: 'name',
        label: 'Outcome Type',
        type: 'input',
        required: true,
        placeholder: 'Outcome Type',
        error: form.errors.name,
        modelValue: form.name,
        updateValue: (value: string) => (form.name = value),
    });

    if (!hasHeadquarterAccess.value) {
        fields.push({
            id: 'headquarter_id',
            label: 'Headquarter Name',
            type: 'select',
            required: true,
            placeholder: 'Select headquarter',
            error: form.errors.headquarter_id,
            options: formatSelectionOptions(props.headquarters, 'id', 'display_name'),
            modelValue: form.headquarter_id,
            updateValue: (value: number) => (form.headquarter_id = value),
        });
    }

    if (!hasCompanyAccess.value && !isHeadquarter.value) {
        fields.push({
            id: 'company_id',
            label: 'Company Name',
            type: 'select',
            required: true,
            placeholder: 'Select company',
            error: form.errors.company_id,
            options: formatSelectionOptions(filteredCompanies.value, 'id', 'display_name'),
            modelValue: form.company_id,
            updateValue: (value: number) => (form.company_id = value),
        });
    }

    return fields;
});

const filteredCompanies = computed(() => {
    if (!form.headquarter_id) return [];
    return props.companies.filter((company) => company.headquarter_id === form.headquarter_id);
});

watch(
    () => form.headquarter_id,
    (newValue) => {
        if (newValue) {
            form.company_id = null;
        }
    },
);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('agent-outcome-types.store'),
            entityName: 'agent outcome type',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Create Agent Outcome Type" />
        <div class="px-4 py-3">
            <Heading title="Agent Outcome Type" pageNumber="P000033" description="Create a new agent outcome type record" />

            <AppCard title="Add New Agent Outcome" :form="form" backRoute="agent-outcome-types.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                                :class="field.class"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
