<script setup lang="ts">
import Collateral from '@/components/customer/Collateral.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { computed, onMounted, reactive, ref } from 'vue';

interface Selection {
    id: number;
    value: string;
}

interface Props {
    customer: any;
    customerTypes: Selection[];
    collateralTypes: Selection[];
    documentTypes: Selection[];
}

interface Address {
    id: number;
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    state: string | null;
    selection_state_id: number | null;
    state_selection: string | null;
    country: string | null;
    selection_country_id: number | null;
    country_selection: string | null;
}

const props = defineProps<Props>();
const openAccordion = ref('0');
const activeTab = ref('personal');
const ordinal = (n: number) => {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
};
const activeDocTab = ref('all');

const formatAddress = (address: Address | null) => {
    if (!address) return 'No address provided';

    const line1 = address.line_1;
    const line2 = address.line_2;
    const line3Parts = [address.postcode, address.city, address.state_selection || address.state, address.country_selection || address.country]
        .filter(Boolean)
        .join(', ');

    return [line1, line2, line3Parts].filter(Boolean).join('\n');
};

const formattedAddresses = computed(() => {
    return props.customer.addresses?.map(formatAddress) ?? [];
});

const tabFlow = computed(() => ({
    personal: props.customer.customer_type === 'Personal' ? 'employment' : 'company',
    employment: 'collateral',
    company: 'collateral',
    collateral: 'document',
    document: null,
}));

const tabBackFlow = computed(() => ({
    employment: 'personal',
    company: 'personal',
    collateral: props.customer.customer_type === 'Personal' ? 'employment' : 'company',
    document: 'collateral',
}));

const tabItems = computed(() => [
    { label: 'Personal Detail', value: 'personal' },
    ...(props.customer.customer_type === 'Personal'
        ? [{ label: 'Employment Details', value: 'employment' }]
        : [{ label: 'Company Details', value: 'company' }]),
    { label: 'Collateral', value: 'collateral' },
    { label: 'Document', value: 'document' },
]);

const docItems = computed(() => [
    { label: 'All', value: 'all' },
    { label: 'Customer Documents', value: 'customer-doc' },
    { label: 'Collateral Documents', value: 'collateral-doc' },
    { label: 'Security Documents', value: 'security-doc' },
]);

const goToNextTab = () => {
    const current = activeTab.value as keyof typeof tabFlow.value;
    const next = tabFlow.value[current];
    if (next) activeTab.value = next;
};

const goToPreviousTab = () => {
    const current = activeTab.value as keyof typeof tabBackFlow.value;
    const prev = tabBackFlow.value[current];
    if (prev) activeTab.value = prev;
};

type FileData = {
    id?: number;
    name: string;
    size: number;
    url: string;
    typeId: number;
    type: string;
    createdAt: string;
    uploadedBy: string;
};

type TabKey = 'customer-doc' | 'collateral-doc' | 'security-doc' | 'all';
const categorizedFiles = reactive<Record<TabKey, FileData[]>>({
    'customer-doc': [],
    'collateral-doc': [],
    'security-doc': [],
    all: [],
});

const CUSTOMER_DOC_ID = props.documentTypes.find((type) => type.value === 'Customer Documents')?.id ?? null;
const COLLATERAL_DOC_ID = props.documentTypes.find((type) => type.value === 'Collateral Documents')?.id ?? null;
const SECURITY_DOC_ID = props.documentTypes.find((type) => type.value === 'Security Documents')?.id ?? null;

const selectedDocIds = ref<(string | number | undefined)[]>([]);
const allDocs = computed(() => {
    return Object.values(categorizedFiles).flat(); // includes all docs from all tabs
});

const selectedFile = computed(() => {
    const id = selectedDocIds.value[0];
    return allDocs.value.find((doc) => doc.id === id) || null;
});

const toggleSelect = (index: number | undefined) => {
    if (selectedDocIds.value[0] === index) {
        selectedDocIds.value = [];
    } else {
        // Only allow one selected index at a time
        selectedDocIds.value = [index];
    }
};

const typeToTabKey: Record<number, TabKey> = {};

if (CUSTOMER_DOC_ID) typeToTabKey[CUSTOMER_DOC_ID] = 'customer-doc';
if (COLLATERAL_DOC_ID) typeToTabKey[COLLATERAL_DOC_ID] = 'collateral-doc';
if (SECURITY_DOC_ID) typeToTabKey[SECURITY_DOC_ID] = 'security-doc';

onMounted(() => {
    if (props.customer.documents) {
        props.customer.documents.forEach((doc) => {
            const baseStorageUrl = `${window.location.origin}/storage/`;
            const rawPath = doc.file?.url || doc.url;
            const fullUrl = rawPath.startsWith('http') ? rawPath : baseStorageUrl + rawPath;

            const fileName = doc.file?.name || rawPath.split('/').pop();
            const extension = fileName?.split('.').pop()?.toLowerCase();

            let type = 'Other';
            if (extension === 'pdf') {
                type = 'PDF';
            } else if (['jpg', 'jpeg'].includes(extension)) {
                type = 'JPG';
            } else if (extension === 'png') {
                type = 'PNG';
            }

            const fileEntry = {
                id: doc.id,
                name: fileName,
                url: fullUrl,
                type,
                size: doc.file?.size,
                typeId: doc.selection_type_id,
                isExisting: true,
                createdAt: new Date(doc.created_at).toLocaleString(),
                uploadedBy: doc['uploaded_by']?.username,
            };

            // Push to 'all'
            categorizedFiles['all'].push(fileEntry);

            // Push to specific tab if valid
            const tabKey = typeToTabKey[doc.selection_type_id];
            if (tabKey) {
                categorizedFiles[tabKey].push(fileEntry);
            }
        });
    }
});

const isImage = (fileName: string): boolean => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ['jpg', 'jpeg', 'png'].includes(ext ?? '');
};

function formatDate(dateString: string | null) {
    if (!dateString) return '-';
    return new Date(dateString).toISOString().slice(0, 10); // Formats as 'YYYY-MM-DD'
}
</script>

<template>
    <AppLayout>
        <Head :title="`Customer: ${customer.name}`" />

        <div class="px-4 py-6">
            <div class="flex items-center justify-between">
                <Heading title="Customer" pageNumber="P000067" description="View Customer Details" />
            </div>
            <Card class="gap-0 py-0">
                <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                    <CardTitle>Customer - {{ customer.name }}</CardTitle>
                </CardHeader>

                <TabsWrapper v-model="activeTab" :tabs="tabItems">
                    <template #personal>
                        <CardContent class="py-4">
                            <Label class="text-[20px]">Personal Details</Label>
                            <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                <template v-if="props.customer.customer_type === 'Personal'">
                                    <div class="space-y-3">
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="customer-type:" class="text-base">Cutomer Type </Label>
                                            <p>{{ props.customer.customer_type }}</p>
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="team">Team </Label>
                                            <p>{{ props.customer.team }}</p>
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="name" class="text-base">Name </Label>
                                            {{ props.customer.name }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="identity_no" class="text-base">I/C No </Label>
                                            {{ props.customer.identity_no }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="dob" class="text-base">Date of Birth </Label>
                                            {{ formatDate(props.customer.birth_date) }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="age" class="text-base">Age </Label>
                                            {{ props.customer.age }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="race" class="text-base">Race </Label>
                                            {{ props.customer.race_selection }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="gender" class="text-base">Gender </Label>
                                            {{ props.customer.gender_selection }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="marital_status" class="text-base">Marital Status </Label>
                                            {{ props.customer.marriage_status_selection }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="nationality" class="text-base">Nationality </Label>
                                            {{ props.customer.nationality_selection }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="education_level" class="text-base">Education Level </Label>
                                            {{ props.customer.education_level_selection }}
                                        </div>
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="space-y-3">
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="team" class="text-base">Team</Label>
                                            <p>{{ props.customer.team }}</p>
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="customer-type:" class="text-base">Cutomer Type</Label>
                                            <p>{{ props.customer.customer_type }}</p>
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="name" class="text-base">Name </Label>
                                            {{ props.customer.name }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="identity_no" class="text-base">New Business Registration No. </Label>
                                            {{ props.customer.identity_no }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="old_business_registration_no" class="text-base">Old Business Registration No. </Label>
                                            {{ props.customer.old_identity_no }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="date_of_registration" class="text-base">Date of Registration </Label>
                                            {{ formatDate(props.customer.registration_date) }}
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="years_of_incorporation" class="text-base">Years of Incorporation </Label>
                                            {{ props.customer.years_of_incorporation }}
                                        </div>
                                    </div>
                                </template>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between px-1">
                                        <Label for="email" class="text-base">Email </Label>
                                        {{ props.customer.email ?? '-' }}
                                    </div>
                                    <div class="flex items-start justify-between px-1">
                                        <Label for="years_of_incorporation" class="text-base"> Contacts </Label>
                                        <div class="flex flex-col text-right">
                                            <div v-for="(contact, index) in props.customer.contacts" :key="index">
                                                ({{ contact.contact_country_selection }}) {{ contact.contact }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <Separator class="my-4" />
                            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                                <div v-for="(address, index) in props.customer.addresses" :key="index" class="space-y-1">
                                    <Label :for="`address-${index}`" class="text-base font-semibold"> Address {{ index + 1 }} </Label>
                                    <p class="whitespace-pre-wrap">
                                        {{ formatAddress(address) }}
                                    </p>
                                </div>
                            </div>
                            <Separator class="my-4" />
                            <div>
                                <div>
                                    <Label for="remark" class="text-base">Remark </Label>
                                    {{ props.customer.remark }}
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                            <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                Next
                                <FaIcon name="chevron-right" />
                            </Button>
                        </CardFooter>
                    </template>
                    <template #employment>
                        <CardContent class="py-4">
                            <Label class="text-[20px]">Employment Details</Label>
                            <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                <div class="flex items-center justify-between px-1">
                                    <Label for="selection_terms_of_employment_id" class="text-base">Terms of Employment </Label>
                                    {{ props.customer.employment.terms_of_employment_selection }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="length_service_year" class="text-base">Length of Service (Year) </Label>
                                    {{ props.customer.employment.length_service_year }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="employer_name" class="text-base">Employment Name </Label>
                                    {{ props.customer.employment.employer_name }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="length_service_month" class="text-base">Length of Service (Month) </Label>
                                    {{ props.customer.employment.length_service_month }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="job_position" class="text-base">Job Position </Label>
                                    {{ props.customer.employment.job_position }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="selection_occupation_id" class="text-base">Occupation </Label>
                                    {{ props.customer.employment.occupation_selection }}
                                </div>
                                <div v-if="props.customer.employment.business_category_selection" class="flex items-center justify-between px-1">
                                    <Label for="business_category" class="text-base">Business Category </Label>
                                    {{ props.customer.employment.business_category_selection }}
                                </div>
                                <Separator class="col-span-2" />
                                <div class="flex items-center justify-between px-1">
                                    <Label for="gross_income" class="text-base">Gross Income (Monthly) </Label>
                                    {{ props.customer.employment.gross_income }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="net_income" class="text-base">Net Income (Monthly) </Label>
                                    {{ props.customer.employment.net_income }}
                                </div>
                                <Separator class="col-span-2" />
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between px-1">
                                        <Label for="employment.telephone" class="text-base">Telephone </Label>
                                        {{ props.customer.employment.telephone_country_selection }} {{ props.customer.employment.telephone }}
                                    </div>
                                    <div class="flex items-center justify-between px-1">
                                        <Label for="name" class="text-base">Mobile Phone </Label>
                                        {{ props.customer.employment.mobile_country_selection }} {{ props.customer.employment.mobile_phone }}
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    <div class="px-1">
                                        <Label for="address" class="py-0 text-base">Address </Label>
                                        <p class="whitespace-pre-wrap">{{ formatAddress(props.customer.employment.address) }}</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                            <Button
                                variant="outline"
                                @click="goToPreviousTab"
                                type="button"
                                class="bg-card text-muted-foreground flex items-center gap-2"
                            >
                                <FaIcon name="chevron-left" /> Back
                            </Button>
                            <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                Next
                                <FaIcon name="chevron-right" />
                            </Button>
                        </CardFooter>
                    </template>
                    <template #company>
                        <CardContent class="py-4">
                            <Label class="text-[20px]">Company Details</Label>
                            <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                <div class="flex items-center justify-between px-1">
                                    <Label for="name" class="text-base">Nature of Business </Label>
                                    {{ props.customer.company.nature_of_business_selection }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="name" class="text-base">Country of Business Operation </Label>
                                    {{ props.customer.company.country_of_business_selection }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="current_paid_up_capital" class="text-base">Current Paid Up Capital (RM) </Label>
                                    {{ props.customer.company.current_paid_up_capital }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="business_turnover" class="text-base">Business Turnover (RM) </Label>
                                    {{ props.customer.company.business_turnover }} {{ formatDate(props.customer.company.business_turnover_date) }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="business_net_income" class="text-base">Business Net Income (RM) </Label>
                                    {{ props.customer.company.business_net_income }} {{ formatDate(props.customer.company.business_net_income_date) }}
                                </div>
                            </div>
                            <Separator class="my-4" />
                            <Accordion type="multiple" class="w-full" collapsible v-model="openAccordion">
                                <AccordionItem v-for="(owner, index) in props.customer.owners" :key="index" :value="String(index)" class="mb-1">
                                    <Card class="gap-0 rounded-xs py-0">
                                        <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                            <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                <FaIcon name="plus" />
                                            </span>
                                            <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                <FaIcon name="minus" />
                                            </span>
                                            <span class="flex-1 py-2 text-left font-medium">{{ ordinal(index + 1) }} Owner </span>
                                        </AccordionTrigger>
                                        <Separator />
                                        <AccordionContent class="p-2">
                                            <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                                <div class="flex items-center justify-between px-1">
                                                    <Label class="text-base"> Owner Type </Label>
                                                    {{ owner.onwer_type }}
                                                </div>
                                                <div></div>
                                                <div class="flex items-center justify-between px-1">
                                                    <Label class="text-base"> Name </Label>
                                                    {{ owner.name }}
                                                </div>
                                                <div class="flex items-center justify-between px-1">
                                                    <Label class="text-base"> Identity No </Label>
                                                    {{ owner.identity_no }}
                                                </div>
                                                <div class="flex items-center justify-between px-1">
                                                    <Label class="text-base"> Nationality </Label>
                                                    {{ owner.nationality_selection }}
                                                </div>
                                                <div class="flex items-center justify-between px-1">
                                                    <Label class="text-base"> Share (%) </Label>
                                                    {{ owner.share_unit }}
                                                </div>
                                            </div>
                                        </AccordionContent>
                                    </Card>
                                </AccordionItem>
                            </Accordion>
                        </CardContent>
                        <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                            <Button
                                variant="outline"
                                @click="goToPreviousTab"
                                type="button"
                                class="bg-card text-muted-foreground flex items-center gap-2"
                            >
                                <FaIcon name="chevron-left" /> Back
                            </Button>
                            <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                Next
                                <FaIcon name="chevron-right" />
                            </Button>
                        </CardFooter>
                    </template>
                    <template #collateral>
                        <CardContent class="py-4">
                            <Collateral :form="props.customer.collaterals" :ordinal="ordinal" isAccordion isShow :collateralTypes="collateralTypes" />
                        </CardContent>
                        <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                            <Button
                                variant="outline"
                                @click="goToPreviousTab"
                                type="button"
                                class="bg-card text-muted-foreground flex items-center gap-2"
                            >
                                <FaIcon name="chevron-left" /> Back
                            </Button>
                            <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                Next
                                <FaIcon name="chevron-right" />
                            </Button>
                        </CardFooter>
                    </template>
                    <template #document>
                        <CardContent class="py-4">
                            <div class="flex gap-4">
                                <!-- Left section (60%) -->
                                <div class="flex w-[75%] flex-col gap-2">
                                    <Card class="h-[420px] w-full rounded-lg py-2">
                                        <CardContent class="px-2">
                                            <TabsWrapper v-model="activeDocTab" :tabs="docItems">
                                                <template #all>
                                                    <CardContent class="px-0">
                                                        <div
                                                            v-if="categorizedFiles['all'].length == 0"
                                                            class="flex h-[380px] items-center justify-center"
                                                        >
                                                            <p>No Documents</p>
                                                        </div>
                                                        <div
                                                            v-else
                                                            class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                        >
                                                            <div
                                                                v-for="(doc, index) in categorizedFiles['all']"
                                                                :key="doc.id"
                                                                @click="toggleSelect(doc.id)"
                                                                :class="[
                                                                    'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                    selectedDocIds.includes(doc.id)
                                                                        ? 'border-blue-500 bg-blue-50'
                                                                        : 'border-transparent hover:border-gray-300',
                                                                ]"
                                                            >
                                                                <div
                                                                    v-if="selectedDocIds.includes(doc.id)"
                                                                    class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                >
                                                                    <span class="text-xs">
                                                                        <FaIcon name="check" class="text-white" />
                                                                    </span>
                                                                </div>

                                                                <!-- Thumbnail -->
                                                                <template v-if="isImage(doc.name)">
                                                                    <img :src="doc.url" alt="preview" class="h-[85px] w-[150px] object-contain" />
                                                                </template>
                                                                <template v-else>
                                                                    <span class="text-[35px]">
                                                                        <FaIcon
                                                                            name="file-pdf"
                                                                            class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                        />
                                                                    </span>
                                                                </template>

                                                                <!-- File name -->
                                                                <a
                                                                    :href="doc.url"
                                                                    target="_blank"
                                                                    class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                >
                                                                    {{ doc.name }}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </template>

                                                <template #customer-doc>
                                                    <CardContent class="px-0">
                                                        <div
                                                            v-if="categorizedFiles['customer-doc'].length == 0"
                                                            class="flex h-[380px] items-center justify-center"
                                                        >
                                                            <p>No Documents</p>
                                                        </div>
                                                        <div
                                                            v-else
                                                            class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                        >
                                                            <div
                                                                v-for="(doc, index) in categorizedFiles['customer-doc']"
                                                                :key="doc.id"
                                                                @click="toggleSelect(doc.id)"
                                                                :class="[
                                                                    'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                    selectedDocIds.includes(doc.id)
                                                                        ? 'border-blue-500 bg-blue-50'
                                                                        : 'border-transparent hover:border-gray-300',
                                                                ]"
                                                            >
                                                                <div
                                                                    v-if="selectedDocIds.includes(doc.id)"
                                                                    class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                >
                                                                    <span class="text-xs">
                                                                        <FaIcon name="check" class="text-white" />
                                                                    </span>
                                                                </div>

                                                                <!-- Thumbnail -->
                                                                <template v-if="isImage(doc.name)">
                                                                    <img :src="doc.url" alt="preview" class="h-[85px] w-[150px] object-contain" />
                                                                </template>
                                                                <template v-else>
                                                                    <span class="text-[35px]">
                                                                        <FaIcon
                                                                            name="file-pdf"
                                                                            class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                        />
                                                                    </span>
                                                                </template>

                                                                <!-- File name -->
                                                                <a
                                                                    :href="doc.url"
                                                                    target="_blank"
                                                                    class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                >
                                                                    {{ doc.name }}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </template>

                                                <template #collateral-doc>
                                                    <CardContent class="px-0">
                                                        <div
                                                            v-if="categorizedFiles['collateral-doc'].length == 0"
                                                            class="flex h-[380px] items-center justify-center"
                                                        >
                                                            <p>No Documents</p>
                                                        </div>
                                                        <div
                                                            v-else
                                                            class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                        >
                                                            <div
                                                                v-for="(doc, index) in categorizedFiles['collateral-doc']"
                                                                :key="doc.id"
                                                                @click="toggleSelect(doc.id)"
                                                                :class="[
                                                                    'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                    selectedDocIds.includes(doc.id)
                                                                        ? 'border-blue-500 bg-blue-50'
                                                                        : 'border-transparent hover:border-gray-300',
                                                                ]"
                                                            >
                                                                <div
                                                                    v-if="selectedDocIds.includes(doc.id)"
                                                                    class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                >
                                                                    <span class="text-xs">
                                                                        <FaIcon name="check" class="text-white" />
                                                                    </span>
                                                                </div>

                                                                <!-- Thumbnail -->
                                                                <template v-if="isImage(doc.name)">
                                                                    <img :src="doc.url" alt="preview" class="h-[85px] w-[150px] object-contain" />
                                                                </template>
                                                                <template v-else>
                                                                    <span class="text-[35px]">
                                                                        <FaIcon
                                                                            name="file-pdf"
                                                                            class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                        />
                                                                    </span>
                                                                </template>

                                                                <!-- File name -->
                                                                <a
                                                                    :href="doc.url"
                                                                    target="_blank"
                                                                    class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                >
                                                                    {{ doc.name }}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </template>

                                                <template #security-doc>
                                                    <CardContent class="px-0">
                                                        <div
                                                            v-if="categorizedFiles['security-doc'].length == 0"
                                                            class="flex h-[380px] items-center justify-center"
                                                        >
                                                            <p>No Documents</p>
                                                        </div>
                                                        <div
                                                            v-else
                                                            class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                        >
                                                            <div
                                                                v-for="(doc, index) in categorizedFiles['security-doc']"
                                                                :key="doc.id"
                                                                @click="toggleSelect(doc.id)"
                                                                :class="[
                                                                    'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                    selectedDocIds.includes(doc.id)
                                                                        ? 'border-blue-500 bg-blue-50'
                                                                        : 'border-transparent hover:border-gray-300',
                                                                ]"
                                                            >
                                                                <div
                                                                    v-if="selectedDocIds.includes(doc.id)"
                                                                    class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                >
                                                                    <span class="text-xs">
                                                                        <FaIcon name="check" class="text-white" />
                                                                    </span>
                                                                </div>

                                                                <!-- Thumbnail -->
                                                                <template v-if="isImage(doc.name)">
                                                                    <img :src="doc.url" alt="preview" class="h-[85px] w-[150px] object-contain" />
                                                                </template>
                                                                <template v-else>
                                                                    <span class="text-[35px]">
                                                                        <FaIcon
                                                                            name="file-pdf"
                                                                            class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                        />
                                                                    </span>
                                                                </template>

                                                                <!-- File name -->
                                                                <a
                                                                    :href="doc.url"
                                                                    target="_blank"
                                                                    class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                >
                                                                    {{ doc.name }}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </template>
                                            </TabsWrapper>
                                        </CardContent>
                                    </Card>
                                </div>
                                <div class="w-[25%]">
                                    <!-- TODO: show selected doc info -->
                                    <Card class="flex h-[500px]" v-if="selectedDocIds.length > 0 && selectedFile">
                                        <CardContent>
                                            <div class="mt-4 w-full max-w-md">
                                                <!-- File name as link -->
                                                <a
                                                    :href="selectedFile.url"
                                                    target="_blank"
                                                    class="mb-3 block truncate text-sm text-blue-700 hover:underline"
                                                >
                                                    {{ selectedFile.name }}
                                                </a>

                                                <!-- Icon -->
                                                <div v-if="isImage(selectedFile.name)" class="mb-6 text-center">
                                                    <img :src="selectedFile.url" alt="preview" class="h-auto max-h-[150px] w-full object-contain" />
                                                </div>
                                                <div v-else class="mb-6 text-center">
                                                    <span class="text-lavender text-[50px]">
                                                        <FaIcon name="file" />
                                                    </span>
                                                </div>

                                                <!-- Details -->
                                                <div class="space-y-4 text-sm">
                                                    <div>
                                                        <p class="font-semibold">File Type:</p>
                                                        <p>{{ selectedFile.type }}</p>
                                                    </div>
                                                    <div>
                                                        <p class="font-semibold">File Size:</p>
                                                        <p>{{ (selectedFile.size / 1024).toFixed(2) }} KB</p>
                                                    </div>
                                                    <div>
                                                        <p class="font-semibold">Created Date:</p>
                                                        <p>{{ selectedFile.createdAt }}</p>
                                                    </div>
                                                    <div>
                                                        <p class="font-semibold">Uploaded By:</p>
                                                        <p>{{ selectedFile.uploadedBy }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                    <Card v-else class="flex h-[500px] items-center justify-center">
                                        <CardContent>
                                            <div class="text-center">
                                                <span class="text-lavender text-[50px]">
                                                    <FaIcon name="file" />
                                                </span>
                                                <p>0 Item</p>
                                                <p>Select a single file to get more information</p>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter class="bg-stone flex justify-end gap-2 px-4 py-3">
                            <Button
                                variant="outline"
                                @click="goToPreviousTab"
                                type="button"
                                class="bg-card text-muted-foreground flex items-center gap-2"
                            >
                                <FaIcon name="chevron-left" /> Back
                            </Button>
                        </CardFooter>
                    </template>
                </TabsWrapper>
            </Card>
        </div>
    </AppLayout>
</template>
