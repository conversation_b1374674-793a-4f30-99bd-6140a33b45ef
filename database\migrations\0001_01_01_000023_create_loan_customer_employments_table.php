<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loan_customer_employments', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Foreign key relationships
            $table->foreignId('loan_customer_profile_id')->constrained('loan_customer_profiles')->cascadeOnDelete();

            // Employment Info
            $table->string('employer_name')->nullable();
            $table->unsignedTinyInteger('length_service_year')->nullable();
            $table->unsignedTinyInteger('length_service_month')->nullable();
            $table->string('job_position')->nullable();

            // Selection references
            $table->foreignId('selection_terms_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('terms', 72)->nullable();
            $table->foreignId('selection_occupation_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('occupation')->nullable();
            $table->foreignId('selection_business_category_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('business_category', 72)->nullable();

            // Income Summary
            $table->decimal('gross_income', 24, 2)->nullable();
            $table->decimal('net_income', 24, 2)->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_employments');
    }
};
