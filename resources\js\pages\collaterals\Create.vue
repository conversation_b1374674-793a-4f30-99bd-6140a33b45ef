<script setup lang="ts">
import Heading from '@/components/Heading.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import AdditionalPropertyOwner from '@/components/collateral/AdditionalPropertyOwner.vue';
import CollateralInfo from '@/components/collateral/CollateralInfo.vue';
import Valuation from '@/components/collateral/Valuation.vue';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { useFormSubmit } from '@/composables/useFormSubmit';
import { useLeaveGuard } from '@/composables/useLeaveGuard';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed, ref, watch } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Selection {
    id: number;
    value: string;
}

interface Props {
    statuses: Record<string, string>;
    customerTypes: Selection[];
    collateralTypes: Selection[];
    propertyTypes: Selection[];
    landCategories: Selection[];
    squareTypes: Selection[];
    landStatuses: Selection[];
    states: Selection[];
    countries: Selection[];
    mobileCountries: Selection[];
    telephoneCountries: Selection[];
}

const props = defineProps<Props>();
const activeTab = ref('info');
const ordinal = (n) => {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
};
const hasUnsavedChanges = ref(true); // set to true when the form is dirty
useLeaveGuard(() => hasUnsavedChanges.value);

type Address = {
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    selection_state_id: number | null;
    state: string | null;
    selection_country_id: number | null;
    country: string | null;
};

type PropertyOwner = {
    name: string;
    identity_no: string;
    selection_telephone_country_id: number | null;
    selection_mobile_country_id: number | null;
    telephone: string | null;
    mobile_phone: string | null;
    remark: string | null;
    address: Address;
};

// Initialize form with empty values
const form = useForm({
    selection_customer_type_id: props.customerTypes[0]?.id ?? null,
    customer_type: '' as string,
    selection_type_id: props.collateralTypes[0]?.id ?? null,
    name: '' as string,
    identity_no: '' as string,
    status: 0,
    remark: null as string | null,
    company_name: null as string | null,
    business_registration_no: null as string | null,

    // Property data
    property: {
        ownership_no: null as string | null,
        lot_number: null as string | null,
        selection_land_category_id: null as number | null,
        land_category: null as string | null,
        land_category_other: null as string | null,
        selection_type_of_property_id: null as number | null,
        type_of_property: null as string | null,
        selection_land_size_unit: (props.squareTypes[0]?.id ?? null) as number | null,
        land_size: null as string | null,
        selection_land_status_id: null as number | null,
        land_status: null as string | null,
        no_syit_piawai: null as string | null,
        certified_plan_no: null as string | null,
        selection_built_up_area_unit: (props.squareTypes[0]?.id ?? null) as number | null,
        built_up_area_of_property: null as string | null,
        city: null as string | null,
        location: null as string | null,
        district: null as string | null,
        address: {
            line_1: null as string | null,
            line_2: null as string | null,
            postcode: null as string | null,
            city: null as string | null,
            selection_state_id: null as number | null,
            state: null as string | null,
            selection_country_id: null as number | null,
            country: null as string | null,
        },
    },

    // Property owners
    property_owners: [] as PropertyOwner[],

    // Valuers
    valuers: [
        {
            valuation_amount: 0,
            valuer: '',
            valuation_received_date: null as string | null,
            land_search_received_date: null as string | null,
            is_primary: true as boolean | null,
        },
    ],
});

// Submit form
const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('collaterals.store'),
            entityName: 'collateral',
        },
    });
};

const goBack = () => {
    if (!hasUnsavedChanges.value) {
        $inertia.visit(route('collaterals.index'));
        return;
    }

    const fakeLink = document.createElement('a');
    fakeLink.href = route('collaterals.index');
    document.body.appendChild(fakeLink);
    fakeLink.click();
    document.body.removeChild(fakeLink);
};

// Watch for collateral type selection changes
watch(
    () => form.selection_type_id,
    (newValue) => {
        if (newValue) {
            const selectedType = props.collateralTypes.find((type) => type.id === newValue);
            if (selectedType) {
                form.type = selectedType.value;
            }
        }
    },
);

const tabItems = computed(() => [
    { label: 'Collateral Info', value: 'info' },
    { label: 'Valuation', value: 'valuation' },
    { label: 'Additional Owner', value: 'owner' },
]);
</script>

<template>
    <AppLayout>
        <Head title="Add Collateral" />
        <div class="px-4 py-3">
            <Heading title="Create Collateral" pageNumber="P000066" description="Add Collateral Details" />
            <form @submit.prevent="submit">
                <Card class="gap-0 py-0">
                    <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                        <CardTitle>Add New Collateral</CardTitle>
                    </CardHeader>
                    <TabsWrapper v-model="activeTab" :tabs="tabItems">
                        <template #info>
                            <!-- Your info content -->
                            <CollateralInfo
                                :form="form"
                                :errors="form.errors"
                                :customerTypes="customerTypes"
                                :collateralTypes="collateralTypes"
                                :propertyTypes="propertyTypes"
                                :landCategories="landCategories"
                                :landStatuses="landStatuses"
                                :squareTypes="squareTypes"
                                :states="states"
                                :countries="countries"
                                :ordinal="ordinal"
                                :goBack="goBack"
                                isFooter
                                :goNext="() => (activeTab = 'valuation')"
                            />
                        </template>

                        <template #valuation>
                            <!-- Your valuation content -->
                            <Valuation
                                :valuers="form.valuers"
                                :errors="form.errors"
                                :ordinal="ordinal"
                                collateralSide
                                :goBack="() => (activeTab = 'info')"
                                :goNext="() => (activeTab = 'owner')"
                            />
                        </template>

                        <template #owner>
                            <!-- Your owner content -->
                            <AdditionalPropertyOwner
                                :property_owners="form.property_owners"
                                :errors="form.errors"
                                :states="states"
                                :countries="countries"
                                :mobileCountries="mobileCountries"
                                :telephoneCountries="telephoneCountries"
                                :ordinal="ordinal"
                                collateralSide
                                :goBack="() => (activeTab = 'valuation')"
                                :submit="submit"
                            />
                        </template>
                    </TabsWrapper>
                </Card>
            </form>
        </div>
    </AppLayout>
</template>
