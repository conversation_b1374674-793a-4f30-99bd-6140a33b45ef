<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';

interface Permission {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
}

interface Props {
    permission: Permission;
}

const props = defineProps<Props>();

const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`Permission - ${permission.name}`" />

        <div class="px-4 py-3">
            <Heading title="Permissions" pageNumber="P000028" description="View details of the permission record" />

            <AppCard title="View Permission" backRoute="permissions.index" :form="form" :itemId="props.permission.id">
                <dl class="grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Permission Name</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.permission.name }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Created At</Label>
                        <dd class="col-span-2 text-right text-sm">{{ formatDateTime(props.permission.created_at) }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Updated At</Label>
                        <dd class="col-span-2 text-right text-sm">{{ formatDateTime(props.permission.updated_at) }}</dd>
                    </div>
                </dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
