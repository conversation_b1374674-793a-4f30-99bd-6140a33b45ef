<?php

namespace Database\Seeders\User;

use App\Enums\User\UserStatus;
use App\Models\AdminProfile;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class TeamLoanReviewerUserSeeder extends Seeder
{
    public function run(): void
    {
        $team = Team::firstWhere('name', 'Company Team 2');
        if (! $team) {
            $this->command->error('Team not found. Please run TeamSeeder first.');

            return;
        }

        $teamLoanReviewerRole = Role::firstWhere('name', 'Team Loan Reviewer');
        if (! $teamLoanReviewerRole) {
            $this->command->error('Team Loan Reviewer role not found. Please run RoleSeeder first.');

            return;
        }

        $teamLoanReviewer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'uuid' => Str::uuid(),
                'username' => 'TeamLoanReviewer',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'remember_token' => Str::random(10),
                'status' => UserStatus::ACTIVE,
            ]
        );

        if (! $teamLoanReviewer->hasRole($teamLoanReviewerRole)) {
            $teamLoanReviewer->assignRole($teamLoanReviewerRole);
        }

        if (! $teamLoanReviewer->adminProfiles()->exists()) {
            $superAdminId = User::where('email', '<EMAIL>')->value('id');

            $adminProfile = AdminProfile::create([
                'code' => 'TA00003',
                'created_by' => $superAdminId,
                'updated_by' => $superAdminId,
            ]);

            $adminProfile->users()->attach($teamLoanReviewer->id);

            $adminProfile->teams()->syncWithoutDetaching([$team->id]);
        }

        $this->command->info('Team loan reviewer user created successfully.');
    }
}
