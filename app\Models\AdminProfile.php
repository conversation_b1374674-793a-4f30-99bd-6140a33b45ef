<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Support\Str;

/**
 * AdminProfile model for managing admin user profiles
 */
class AdminProfile extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'code',
        'contact_no',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the users associated with this admin profile.
     */
    public function users(): MorphToMany
    {
        return $this->morphToMany(User::class, 'userable');
    }

    /**
     * Get the headquarters associated with this admin profile.
     */
    public function headquarters(): MorphToMany
    {
        return $this->morphToMany(Headquarter::class, 'headquarterable');
    }

    /**
     * Get the companies associated with this admin profile.
     */
    public function companies(): MorphToMany
    {
        return $this->morphToMany(Company::class, 'companyable');
    }

    /**
     * Get the teams associated with this admin profile.
     */
    public function teams(): MorphToMany
    {
        return $this->morphToMany(Team::class, 'teamable');
    }

    /**
     * Get the headquarter associated with this admin profile.
     */
    public function headquarter(): ?Headquarter
    {
        return $this->companies()->whereNull('headquarter_id')->first();
    }

    /**
     * Get the company (non-headquarter) associated with this admin profile.
     */
    public function company(): ?Company
    {
        return $this->companies()->whereNotNull('headquarter_id')->first();
    }

    /**
     * Get the team associated with this admin profile.
     */
    public function team(): ?Team
    {
        return $this->teams()->first();
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = (string) Str::uuid();
            }
        });
    }
}
