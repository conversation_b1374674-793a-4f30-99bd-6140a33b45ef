<?php

namespace App\Http\Requests\Agents;

use App\Http\Requests\BaseRequest;
use App\Models\Company;

class StoreOutcomeTypeRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'headquarter_id' => ['required', 'exists:companies,id'],
            'company_id' => [
                'required',
                'exists:companies,id',
                function ($attribute, $value, $fail) {
                    $company = Company::find($value);
                    if (! $company || $company->headquarter_id != $this->headquarter_id) {
                        $fail('The selected company does not belong to the selected headquarter.');
                    }
                },
            ],
        ];
    }
}
