<?php

namespace Database\Seeders\User;

use App\Enums\AccessControl\RoleName;
use App\Enums\User\UserStatus;
use App\Models\AdminProfile;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class SuperAdminSeeder extends Seeder
{
    public function run(): void
    {
        $superAdminRole = Role::firstOrCreate([
            'name' => RoleName::SUPER_ADMIN,
            'level' => 999,
        ]);

        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'Superadmin',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'remember_token' => Str::random(10),
                'status' => UserStatus::ACTIVE,
            ]
        );

        if (! $superAdmin->hasRole($superAdminRole)) {
            $superAdmin->assignRole($superAdminRole);
        }

        if (! $superAdmin->adminProfiles()->exists()) {
            $adminProfile = AdminProfile::create([
                'code' => 'SA00001',
                'created_by' => $superAdmin->id,
                'updated_by' => $superAdmin->id,
            ]);

            $adminProfile->users()->attach($superAdmin->id);
        }

        $this->command->info('Super administrator user created successfully.');
    }
}
